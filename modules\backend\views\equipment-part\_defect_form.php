<?php
use yii\helpers\Html;
use app\assets\Select2Asset;

Select2Asset::register($this);
?>

<form id="part-defect-form">
    <input type="hidden" name="id" value="<?= $model->id ?>">

    <div class="form-group">
        <label class="form-label"><?= Yii::t('app', 'part_name') ?></label>
        <p><strong><?= Html::encode($model->name) ?></strong></p>
    </div>

    <div class="form-group">
        <label class="form-label"><?= Yii::t('app', 'available_quantity') ?></label>
        <p><strong><?= $model->quantity ?></strong></p>
    </div>

    <div class="form-group">
        <label class="form-label"><?= Yii::t('app', 'defect_quantity') ?></label>
        <input type="number" class="form-control" name="quantity" min="1" max="<?= $model->quantity ?>" required>
        <div class="invalid-feedback" id="quantity-error"></div>
    </div>

    <div class="form-group">
        <label class="form-label"><?= Yii::t('app', 'defect_reason') ?></label>
        <textarea class="form-control" name="comment" rows="3" required></textarea>
        <div class="invalid-feedback" id="comment-error"></div>
    </div>
</form>
