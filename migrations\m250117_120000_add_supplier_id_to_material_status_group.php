<?php

use yii\db\Migration;

/**
 * Добавляет поле supplier_id в таблицу material_status_group
 * для отслеживания поставщика при возврате материалов
 */
class m250117_120000_add_supplier_id_to_material_status_group extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        // Добавляем поле supplier_id
        $this->addColumn('{{%material_status_group}}', 'supplier_id', $this->bigInteger()->null());

        // Добавляем внешний ключ
        $this->addForeignKey(
            'fk-material_status_group-supplier_id',
            '{{%material_status_group}}',
            'supplier_id',
            '{{%supplier}}',
            'id',
            'SET NULL'
        );

        // Добавляем индекс для ускорения поиска
        $this->createIndex(
            'idx-material_status_group-supplier_id',
            '{{%material_status_group}}',
            'supplier_id'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        // Удаляем индекс
        $this->dropIndex('idx-material_status_group-supplier_id', '{{%material_status_group}}');

        // Удаляем внешний ключ
        $this->dropForeignKey('fk-material_status_group-supplier_id', '{{%material_status_group}}');

        // Удаляем поле
        $this->dropColumn('{{%material_status_group}}', 'supplier_id');
    }
}
