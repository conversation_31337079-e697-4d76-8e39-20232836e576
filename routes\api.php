<?php

return [
    // Маршруты для управления возвратами материалов
    'GET api/raw-material/return-material/<id:\d+>' => 'api/raw-material/get-return-material',
    'PUT api/raw-material/return-material/<id:\d+>' => 'api/raw-material/update-return-material',
    'DELETE api/raw-material/return-material/<id:\d+>' => 'api/raw-material/delete-return-material',
    
    // Общие API маршруты (должны быть в конце для правильного порядка приоритета)
    'api/<controller:[\w-]+>/<action:[\w-]+>/<id:\d+>' => 'api/<controller>/<action>',
    'api/<controller:[\w-]+>/<action:[\w-]+>' => 'api/<controller>/<action>',
    'api/<controller:[\w-]+>' => 'api/<controller>/index',
];
