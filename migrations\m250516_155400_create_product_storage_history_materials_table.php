<?php

use yii\db\Migration;

/**
 * Класс миграции для создания таблицы product_storage_history_materials
 */
class m250516_155400_create_product_storage_history_materials_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%product_storage_history_materials}}', [
            'id' => $this->bigPrimaryKey(),
            'product_storage_history_id' => $this->bigInteger()->notNull(),
            'material_id' => $this->bigInteger()->notNull(),
            'quantity' => $this->decimal(10, 2)->notNull(),
            'is_alternative' => $this->boolean()->defaultValue(false),
            'original_material_id' => $this->bigInteger()->null(), // если это альтернативный материал
            'created_at' => $this->timestamp()->notNull(),
        ]);

        // Добавляем внешние ключи
        $this->addForeignKey(
            'fk-pshm-product_storage_history_id',
            '{{%product_storage_history_materials}}',
            'product_storage_history_id',
            '{{%product_storage_history}}',
            'id',
            'CASCADE',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk-pshm-material_id',
            '{{%product_storage_history_materials}}',
            'material_id',
            '{{%material}}',
            'id',
            'CASCADE',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk-pshm-original_material_id',
            '{{%product_storage_history_materials}}',
            'original_material_id',
            '{{%material}}',
            'id',
            'SET NULL',
            'CASCADE'
        );

        // Добавляем индексы для ускорения поиска
        $this->createIndex(
            'idx-pshm-product_storage_history_id',
            '{{%product_storage_history_materials}}',
            'product_storage_history_id'
        );

        $this->createIndex(
            'idx-pshm-material_id',
            '{{%product_storage_history_materials}}',
            'material_id'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        // Удаляем внешние ключи
        $this->dropForeignKey('fk-pshm-product_storage_history_id', '{{%product_storage_history_materials}}');
        $this->dropForeignKey('fk-pshm-material_id', '{{%product_storage_history_materials}}');
        $this->dropForeignKey('fk-pshm-original_material_id', '{{%product_storage_history_materials}}');

        // Удаляем индексы
        $this->dropIndex('idx-pshm-product_storage_history_id', '{{%product_storage_history_materials}}');
        $this->dropIndex('idx-pshm-material_id', '{{%product_storage_history_materials}}');

        // Удаляем таблицу
        $this->dropTable('{{%product_storage_history_materials}}');
    }
}
