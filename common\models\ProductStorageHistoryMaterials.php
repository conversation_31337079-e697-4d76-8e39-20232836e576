<?php

namespace app\common\models;

use Yii;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "product_storage_history_materials".
 *
 * @property int $id
 * @property int $product_storage_history_id
 * @property int $material_id
 * @property float $quantity
 * @property bool $is_alternative
 * @property int|null $original_material_id
 * @property string $created_at
 *
 * @property Material $material
 * @property Material $originalMaterial
 * @property ProductStorageHistory $productStorageHistory
 */
class ProductStorageHistoryMaterials extends ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'product_storage_history_materials';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['product_storage_history_id', 'material_id', 'quantity'], 'required'],
            [['product_storage_history_id', 'material_id', 'original_material_id'], 'integer'],
            [['quantity'], 'number'],
            [['is_alternative'], 'boolean'],
            [['created_at'], 'safe'],
            [['product_storage_history_id'], 'exist', 'skipOnError' => true, 'targetClass' => ProductStorageHistory::class, 'targetAttribute' => ['product_storage_history_id' => 'id']],
            [['material_id'], 'exist', 'skipOnError' => true, 'targetClass' => Material::class, 'targetAttribute' => ['material_id' => 'id']],
            [['original_material_id'], 'exist', 'skipOnError' => true, 'targetClass' => Material::class, 'targetAttribute' => ['original_material_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'product_storage_history_id' => 'ID истории склада',
            'material_id' => 'ID материала',
            'quantity' => 'Количество',
            'is_alternative' => 'Альтернативный материал?',
            'original_material_id' => 'ID оригинального материала',
            'created_at' => 'Создано',
        ];
    }

    /**
     * Gets query for [[Material]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getMaterial()
    {
        return $this->hasOne(Material::class, ['id' => 'material_id']);
    }

    /**
     * Gets query for [[OriginalMaterial]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getOriginalMaterial()
    {
        return $this->hasOne(Material::class, ['id' => 'original_material_id']);
    }

    /**
     * Gets query for [[ProductStorageHistory]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getProductStorageHistory()
    {
        return $this->hasOne(ProductStorageHistory::class, ['id' => 'product_storage_history_id']);
    }
}
