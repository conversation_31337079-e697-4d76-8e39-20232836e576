<?php

use yii\db\Migration;

/**
 * Handles the creation of tables for equipment parts management
 */
class m250601_000001_create_equipment_parts_tables extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        // Создаем таблицу запчастей оборудования
        $this->createTable('equipment_parts', [
            'id' => $this->bigPrimaryKey(),
            'equipment_id' => $this->bigInteger()->null(),
            'name' => $this->string(255)->notNull(),
            'photo' => $this->string(255)->notNull(),
            'description' => $this->text(),
            'price' => $this->decimal(10, 2)->null(),
            'source_type' => $this->integer()->notNull(),
            'status' => $this->integer()->notNull(),
            'installation_date' => $this->date()->null(),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'deleted_at' => $this->timestamp()->null(),
        ]);

        // Создаем внешний ключ на таблицу equipment
        $this->addForeignKey(
            'fk-equipment_parts-equipment_id',
            'equipment_parts',
            'equipment_id',
            'equipment',
            'id',
            'SET NULL'
        );

        // Создаем таблицу истории запчастей
        $this->createTable('equipment_part_history', [
            'id' => $this->bigPrimaryKey(),
            'equipment_part_id' => $this->bigInteger()->notNull(),
            'equipment_id' => $this->bigInteger()->null(),
            'action_type' => $this->integer()->notNull(),
            'status_before' => $this->integer()->null(),
            'status_after' => $this->integer()->null(),
            'comment' => $this->text(),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'created_by' => $this->bigInteger()->null(),
        ]);

        // Создаем внешний ключ на таблицу equipment_parts
        $this->addForeignKey(
            'fk-equipment_part_history-equipment_part_id',
            'equipment_part_history',
            'equipment_part_id',
            'equipment_parts',
            'id',
            'CASCADE'
        );

        // Создаем внешний ключ на таблицу equipment
        $this->addForeignKey(
            'fk-equipment_part_history-equipment_id',
            'equipment_part_history',
            'equipment_id',
            'equipment',
            'id',
            'SET NULL'
        );

        // Создаем внешний ключ на таблицу users
        $this->addForeignKey(
            'fk-equipment_part_history-created_by',
            'equipment_part_history',
            'created_by',
            'users',
            'id',
            'SET NULL'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        // Удаляем внешние ключи
        $this->dropForeignKey('fk-equipment_part_history-created_by', 'equipment_part_history');
        $this->dropForeignKey('fk-equipment_part_history-equipment_id', 'equipment_part_history');
        $this->dropForeignKey('fk-equipment_part_history-equipment_part_id', 'equipment_part_history');
        $this->dropForeignKey('fk-equipment_parts-equipment_id', 'equipment_parts');

        // Удаляем таблицы
        $this->dropTable('equipment_part_history');
        $this->dropTable('equipment_parts');
    }
}
