<?php

use app\common\models\EquipmentPart;
use yii\helpers\Html;

$statuses = EquipmentPart::getStatusLabels();
?>

<?php if($result): ?>
    <div>
        <table id="part-grid-view" class="table table-bordered table-striped compact">
            <thead>
                <th><?= Yii::t("app", "Photo") ?></th>
                <th><?= Yii::t("app", "Name") ?></th>
                <th><?= Yii::t("app", "Quantity") ?></th>
                <th><?= Yii::t("app", "Price") ?></th>
                <th><?= Yii::t("app", "last_purchase_date") ?></th>
                <th><?= Yii::t("app", "Status") ?></th>
                <th><?= Yii::t("app", "comment") ?></th>
                <th><?= Yii::t("app", "actions") ?></th>
            </thead>
            <tbody>
            <?php foreach ($result as $model): ?>
                <tr>
                    <td>
                        <?php if ($model['photo']): ?>
                            <img src="/uploads/equipment_parts/<?= Html::encode($model['photo']) ?>"
                                 alt="Part"
                                 style="max-width: 50px; cursor: pointer;"
                                 class="part-photo"
                                 data-id="<?= $model['id'] ?>">
                        <?php endif; ?>
                    </td>
                    <td><?= Html::encode($model['name']) ?></td>
                    <td>
                        <?php
                        $totalQuantity = $model['quantity'] ?? 0;
                        $assignedQuantity = $model['assigned_quantity'] ?? 0;
                        $availableQuantity = $totalQuantity - $assignedQuantity;
                        ?>
                        <span class="badge badge-<?= $totalQuantity > 0 ? 'success' : 'danger' ?>">
                            <?= Yii::t('app', 'Total') ?>: <?= $totalQuantity ?>
                        </span>
                        <?php if (isset($model['assigned_quantity'])): ?>
                            <br><span class="badge badge-warning">
                                <?= Yii::t('app', 'Assigned') ?>: <?= $assignedQuantity ?>
                            </span>
                            <br><span class="badge badge-<?= $availableQuantity > 0 ? 'info' : 'secondary' ?>">
                                <?= Yii::t('app', 'Available') ?>: <?= $availableQuantity ?>
                            </span>
                        <?php endif; ?>
                    </td>
                    <td><?= $model['price'] ? Yii::$app->formatter->asDecimal($model['price'], 2) : '0.00' ?></td>
                    <td data-order="<?= $model['last_purchase_date'] ? strtotime($model['last_purchase_date']) : 0 ?>">
                        <?= $model['last_purchase_date'] ? date('d.m.Y', strtotime($model['last_purchase_date'])) : '-' ?>
                    </td>
                    <td>
                        <?php if (isset($model['status'])): ?>
                            <span class="badge badge-<?= $model['status'] == EquipmentPart::STATUS_INACTIVE ? 'danger' : ($model['status'] == EquipmentPart::STATUS_ACTIVE ? 'success' : 'warning') ?>">
                                <?= $statuses[$model['status']] ?? Yii::t('app', 'unknown') ?>
                            </span>
                        <?php else: ?>
                            <span class="badge badge-info"><?= Yii::t('app', 'mixed') ?></span>
                        <?php endif; ?>
                    </td>
                    <td><?= Html::encode($model['comment'] ?? '-') ?></td>
                    <td>
                        <div class="dropdown d-inline">
                            <a href="#" class="badge badge-info dropdown-toggle" data-toggle="dropdown">
                                <?php echo Yii::t("app", "detail"); ?>
                            </a>
                            <div class="dropdown-menu">
                                <?php if (!isset($model['deleted_at']) || $model['deleted_at'] == NULL): ?>
                                    <a href="#" class="dropdown-item part-update" data-toggle="modal" data-target="#ideal-mini-modal" data-id="<?= Html::encode($model['id']) ?>">
                                        <?= Yii::t("app", "edit") ?>
                                    </a>
                                    <a href="#" class="dropdown-item part-income" data-toggle="modal" data-target="#ideal-mini-modal" data-id="<?= Html::encode($model['id']) ?>">
                                        <?= Yii::t("app", "income") ?>
                                    </a>
                                    <a href="<?= \yii\helpers\Url::to(['/backend/equipment-part/history', 'id' => $model['id']]) ?>" class="dropdown-item">
                                        <?= Yii::t("app", "history") ?>
                                    </a>
                                    <a href="#" class="dropdown-item part-defect" data-toggle="modal" data-target="#ideal-mini-modal" data-id="<?= Html::encode($model['id']) ?>">
                                        <?= Yii::t("app", "defect") ?>
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </td>
                </tr>
            <?php endforeach; ?>
            </tbody>
        </table>
    </div>
<?php else: ?>
    <p><?= Yii::t('app', 'no_data_available') ?></p>
<?php endif; ?>
