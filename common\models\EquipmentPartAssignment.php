<?php

namespace app\common\models;

use Yii;
use yii\db\ActiveRecord;
use app\modules\backend\models\Users;
use yii\behaviors\TimestampBehavior;

/**
 * This is the model class for table "equipment_part_assignments".
 *
 * @property int $id
 * @property int $equipment_id
 * @property int $equipment_part_id
 * @property int $quantity
 * @property string|null $installation_date
 * @property int $status
 * @property string|null $comment
 * @property string $created_at
 * @property string $updated_at
 * @property int|null $created_by
 *
 * @property Equipment $equipment
 * @property EquipmentPart $equipmentPart
 * @property Users $createdBy
 */
class EquipmentPartAssignment extends ActiveRecord
{
    // Статусы назначения
    const STATUS_ACTIVE = 1;    // Активно установлена
    const STATUS_REMOVED = 0;   // Снята с оборудования
    const STATUS_REPAIR = 2;    // В ремонте вместе с оборудованием

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'equipment_part_assignments';
    }

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'createdAtAttribute' => 'created_at',
                'updatedAtAttribute' => 'updated_at',
                'value' => function() {
                    return date('Y-m-d H:i:s');
                },
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['equipment_id', 'equipment_part_id', 'quantity'], 'required'],
            [['equipment_id', 'equipment_part_id', 'quantity', 'status', 'created_by', 'active_quantity', 'repair_quantity'], 'integer'],
            [['installation_date', 'created_at', 'updated_at'], 'safe'],
            [['comment'], 'string'],
            [['quantity'], 'integer', 'min' => 1],
            [['active_quantity', 'repair_quantity'], 'integer', 'min' => 0],
            [['status'], 'in', 'range' => [self::STATUS_ACTIVE, self::STATUS_REMOVED, self::STATUS_REPAIR]],
            [['status'], 'default', 'value' => self::STATUS_ACTIVE],
            [['equipment_id'], 'exist', 'skipOnError' => true, 'targetClass' => Equipment::class, 'targetAttribute' => ['equipment_id' => 'id']],
            [['equipment_part_id'], 'exist', 'skipOnError' => true, 'targetClass' => EquipmentPart::class, 'targetAttribute' => ['equipment_part_id' => 'id']],
            [['created_by'], 'exist', 'skipOnError' => true, 'targetClass' => Users::class, 'targetAttribute' => ['created_by' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'equipment_id' => Yii::t('app', 'Equipment'),
            'equipment_part_id' => Yii::t('app', 'Equipment Part'),
            'quantity' => Yii::t('app', 'Quantity'),
            'installation_date' => Yii::t('app', 'Installation Date'),
            'status' => Yii::t('app', 'Status'),
            'comment' => Yii::t('app', 'Comment'),
            'created_at' => Yii::t('app', 'Created At'),
            'updated_at' => Yii::t('app', 'Updated At'),
            'created_by' => Yii::t('app', 'Created By'),
        ];
    }

    /**
     * Gets query for [[Equipment]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getEquipment()
    {
        return $this->hasOne(Equipment::class, ['id' => 'equipment_id']);
    }

    /**
     * Gets query for [[EquipmentPart]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getEquipmentPart()
    {
        return $this->hasOne(EquipmentPart::class, ['id' => 'equipment_part_id']);
    }

    /**
     * Gets query for [[CreatedBy]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getCreatedBy()
    {
        return $this->hasOne(Users::class, ['id' => 'created_by']);
    }

    /**
     * Get status labels
     * @return array
     */
    public static function getStatusLabels()
    {
        return [
            self::STATUS_ACTIVE => Yii::t('app', 'active'),
            self::STATUS_REMOVED => Yii::t('app', 'removed'),
            self::STATUS_REPAIR => Yii::t('app', 'repair'),
        ];
    }

    /**
     * Get status label
     * @param int $status
     * @return string
     */
    public static function getStatusLabel($status)
    {
        return self::getStatusLabels()[$status] ?? Yii::t('app', 'unknown');
    }

    /**
     * Before save event
     * @param bool $insert
     * @return bool
     */
    public function beforeSave($insert)
    {
        if (parent::beforeSave($insert)) {
            // Устанавливаем пользователя при создании
            if ($insert && !$this->created_by) {
                $this->created_by = Yii::$app->user->id;
            }

            // ✅ ИСПРАВЛЕНИЕ: Автоматически инициализируем количества при создании
            if ($insert) {
                // Если не установлены active_quantity и repair_quantity, инициализируем их
                if ($this->active_quantity === null && $this->repair_quantity === null) {
                    if ($this->status == self::STATUS_ACTIVE) {
                        $this->active_quantity = $this->quantity;
                        $this->repair_quantity = 0;
                    } elseif ($this->status == self::STATUS_REPAIR) {
                        $this->active_quantity = 0;
                        $this->repair_quantity = $this->quantity;
                    } else {
                        $this->active_quantity = 0;
                        $this->repair_quantity = 0;
                    }
                }
            }

            return true;
        }
        return false;
    }

    /**
     * After save event
     * @param bool $insert
     * @param array $changedAttributes
     */
    public function afterSave($insert, $changedAttributes)
    {
        parent::afterSave($insert, $changedAttributes);

        if ($insert) {
            // Создаем запись в истории движений при создании назначения
            // НО ТОЛЬКО если это не назначение для ремонта, созданное программно
            if ($this->status != self::STATUS_REPAIR) {
                $this->createMovementRecord('install');
            }
        } else {
            // Проверяем изменения статуса
            if (isset($changedAttributes['status'])) {
                $oldStatus = $changedAttributes['status'];
                $newStatus = $this->status;

                if ($oldStatus != $newStatus) {
                    if ($newStatus == self::STATUS_REMOVED) {
                        $this->createMovementRecord('remove');
                    } elseif ($newStatus == self::STATUS_ACTIVE && $oldStatus == self::STATUS_REMOVED) {
                        $this->createMovementRecord('install');
                    }
                    // НЕ создаем записи для перехода в ремонт - это делается в контроллере
                }
            }
        }
    }

    /**
     * Create movement record
     * @param string $action
     */
    private function createMovementRecord($action)
    {
        // Создаем запись в истории назначений, но НЕ создаем движение
        // которое автоматически списывает количество.
        // Количество списывается только логически через расчет available_quantity

        // Можно добавить запись в equipment_part_history для отслеживания
        $part = $this->equipmentPart;
        if ($part) {
            $part->addHistory(
                $action == 'install' ? EquipmentPart::ACTION_INSTALL : EquipmentPart::ACTION_REMOVE,
                null,
                null,
                $this->comment ?: Yii::t('app', 'Assigned {quantity} units to equipment: {equipment}', [
                    'quantity' => $this->quantity,
                    'equipment' => $this->equipment->name ?? 'Unknown'
                ]),
                $this->equipment_id
            );
        }
    }

    /**
     * Get total assigned quantity for a part
     * @param int $partId
     * @return int
     */
    public static function getTotalAssignedQuantity($partId)
    {
        return self::find()
            ->where(['equipment_part_id' => $partId])
            ->andWhere(['status' => [self::STATUS_ACTIVE, self::STATUS_REPAIR]])
            ->sum('quantity') ?: 0;
    }

    /**
     * Get available quantity for a part
     * @param int $partId
     * @return int
     */
    public static function getAvailableQuantity($partId)
    {
        $part = EquipmentPart::findOne($partId);
        if (!$part) {
            return 0;
        }

        $assignedQuantity = self::getTotalAssignedQuantity($partId);
        return max(0, $part->quantity - $assignedQuantity);
    }

    /**
     * Получить общее количество запчастей
     * @return int
     */
    public function getTotalQuantity()
    {
        return ($this->active_quantity ?? 0) + ($this->repair_quantity ?? 0);
    }

    /**
     * Отправить количество в ремонт
     * @param int $quantity
     * @return bool
     */
    public function sendToRepair($quantity)
    {
        if ($quantity > $this->active_quantity) {
            return false;
        }

        $this->active_quantity -= $quantity;
        $this->repair_quantity += $quantity;
        $this->quantity = $this->getTotalQuantity();

        return $this->save();
    }

    /**
     * Вернуть количество из ремонта
     * @param int $quantity
     * @return bool
     */
    public function returnFromRepair($quantity)
    {
        if ($quantity > $this->repair_quantity) {
            return false;
        }

        $this->repair_quantity -= $quantity;
        $this->active_quantity += $quantity;
        $this->quantity = $this->getTotalQuantity();

        return $this->save();
    }
}
