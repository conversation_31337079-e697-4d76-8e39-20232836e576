<?php
use yii\helpers\Html;
use app\common\models\EquipmentPart;
?>

<div class="equipment-parts-tab">
    <div class="mb-3">
        <a href="#" class="btn btn-primary part-create-for-equipment" data-toggle="modal" data-target="#ideal-mini-modal" data-equipment-id="<?= $model->id ?>">
            <?= Yii::t("app", "Add Part") ?>
        </a>
    </div>
    
    <?php if (empty($parts)): ?>
        <div class="alert alert-info">
            <?= Yii::t('app', 'No parts attached to this equipment') ?>
        </div>
    <?php else: ?>
        <table class="table table-bordered table-striped">
            <thead>
                <tr>
                    <th><?= Yii::t("app", "Photo") ?></th>
                    <th><?= Yii::t("app", "Name") ?></th>
                    <th><?= Yii::t("app", "Source Type") ?></th>
                    <th><?= Yii::t("app", "Status") ?></th>
                    <th><?= Yii::t("app", "Installation Date") ?></th>
                    <th><?= Yii::t("app", "actions") ?></th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($parts as $part): ?>
                    <tr>
                        <td>
                            <?php if ($part->photo): ?>
                                <img src="/uploads/equipment_parts/<?= Html::encode($part->photo) ?>" 
                                     alt="Part" 
                                     style="max-width: 50px; cursor: pointer;"
                                     class="part-photo"
                                     data-id="<?= $part->id ?>">
                            <?php endif; ?>
                        </td>
                        <td><?= Html::encode($part->name) ?></td>
                        <td>
                            <span class="badge badge-<?= $part->source_type == EquipmentPart::SOURCE_PURCHASED ? 'info' : 'secondary' ?>">
                                <?= EquipmentPart::getSourceTypeLabel($part->source_type) ?>
                            </span>
                        </td>
                        <td>
                            <span class="badge badge-<?= $part->status == EquipmentPart::STATUS_INACTIVE ? 'danger' : ($part->status == EquipmentPart::STATUS_ACTIVE ? 'success' : 'warning') ?>">
                                <?= EquipmentPart::getStatusLabel($part->status) ?>
                            </span>
                        </td>
                        <td>
                            <?= $part->installation_date ? Yii::$app->formatter->asDate($part->installation_date) : '-' ?>
                        </td>
                        <td>
                            <div class="dropdown d-inline">
                                <a href="#" class="badge badge-info dropdown-toggle" data-toggle="dropdown">
                                    <?php echo Yii::t("app", "detail"); ?>
                                </a>
                                <div class="dropdown-menu">
                                    <a href="#" class="dropdown-item part-update" data-toggle="modal" data-target="#ideal-mini-modal" data-id="<?= Html::encode($part->id) ?>">
                                        <?= Yii::t("app", "edit") ?>
                                    </a>
                                    
                                    <a href="#" class="dropdown-item part-change-status" data-toggle="modal" data-target="#ideal-mini-modal" data-id="<?= Html::encode($part->id) ?>">
                                        <?= Yii::t("app", "change_status") ?>
                                    </a>
                                    
                                    <a href="#" class="dropdown-item part-history" data-toggle="modal" data-target="#ideal-large-modal-without-save" data-id="<?= Html::encode($part->id) ?>">
                                        <?= Yii::t("app", "history") ?>
                                    </a>
                                    
                                    <a href="#" class="dropdown-item part-detach" data-toggle="modal" data-target="#ideal-mini-modal" data-id="<?= Html::encode($part->id) ?>">
                                        <?= Yii::t("app", "detach_from_equipment") ?>
                                    </a>
                                </div>
                            </div>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    <?php endif; ?>
</div>

<script>
$(document).ready(function() {
    // Инициализация кнопки добавления запчасти для конкретного оборудования
    $('.part-create-for-equipment').on('click', function() {
        var equipmentId = $(this).data('equipment-id');
        $.ajax({
            url: '/backend/equipment-part/create',
            dataType: 'json',
            type: 'GET',
            success: function(response) {
                $('#ideal-mini-modal .modal-title').html("<?= Yii::t('app', 'Add Part') ?>");
                $('#ideal-mini-modal .modal-body').html(response.content);
                $('#ideal-mini-modal .mini-button').addClass("part-create-button");
                
                // Предварительно выбираем оборудование
                $('select[name="equipment_id"]').val(equipmentId).trigger('change');
                
                // Инициализируем Select2
                $('.select2').select2({
                    width: '100%',
                    language: {
                        noResults: function() {
                            return "Натижа топилмади";
                        }
                    }
                });
            },
            error: function(xhr, textStatus, errorThrown) {
                console.error('AJAX Error:', xhr.statusText, errorThrown);
            }
        });
    });
});
</script>
