<?php
use yii\helpers\Html;
use app\assets\Select2Asset;
use app\common\models\EquipmentPart;

Select2Asset::register($this);
?>

<form id="part-detach-form">
    <input type="hidden" name="id" value="<?= $model->id ?>">
    
    <div class="form-group">
        <label class="form-label"><?= Yii::t('app', 'Current Equipment') ?></label>
        <p><strong><?= Html::encode($model->equipment->name ?? '') ?></strong></p>
    </div>
    
    <div class="form-group">
        <label class="form-label"><?= Yii::t('app', 'Action') ?></label>
        <select class="form-control select2" name="status" required>
            <option value="<?= EquipmentPart::STATUS_RESERVE ?>"><?= Yii::t('app', 'Move to reserve') ?></option>
            <option value="<?= EquipmentPart::STATUS_INACTIVE ?>"><?= Yii::t('app', 'Decommission') ?></option>
        </select>
    </div>
    
    <div class="form-group">
        <label class="form-label"><?= Yii::t('app', 'comment') ?></label>
        <textarea class="form-control" name="comment" rows="3"></textarea>
    </div>
</form>
