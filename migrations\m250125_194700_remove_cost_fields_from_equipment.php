<?php

use yii\db\Migration;

/**
 * Removes initial_cost and useful_life_years fields from equipment table
 */
class m250125_194700_remove_cost_fields_from_equipment extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        // Удаляем поля initial_cost и useful_life_years из таблицы equipment
        $this->dropColumn('equipment', 'initial_cost');
        $this->dropColumn('equipment', 'useful_life_years');
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        // Восстанавливаем поля при откате миграции
        $this->addColumn('equipment', 'initial_cost', $this->decimal(10, 2)->notNull()->defaultValue(0));
        $this->addColumn('equipment', 'useful_life_years', $this->integer()->notNull()->defaultValue(1));
    }
}
