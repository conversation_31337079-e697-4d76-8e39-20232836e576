<?php
use yii\helpers\Html;
use yii\helpers\Url;
use yii\web\JqueryAsset;
use yii\bootstrap4\BootstrapAsset;

/* @var $this yii\web\View */
/* @var $returnGroups array */

$this->title = Yii::t('app', 'material_return');
$this->params['breadcrumbs'][] = $this->title;

// Регистрируем jQuery и Bootstrap
JqueryAsset::register($this);
BootstrapAsset::register($this);
$this->registerMetaTag([
    'name' => 'csrf-token',
    'content' => Yii::$app->request->csrfToken,
]);

?>

<div class="material-return-index">
    <!-- Ваш HTML-код без изменений -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-undo-alt"></i>
                        <?= Html::encode($this->title) ?>
                    </h3>
                </div>
                <div class="card-body">
                    <?php if (empty($returnGroups)): ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <?= Yii::t('app', 'no_pending_returns') ?>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th><?= Yii::t('app', 'created_at') ?></th>
                                        <th><?= Yii::t('app', 'user') ?></th>
                                        <th><?= Yii::t('app', 'supplier') ?></th>
                                        <th><?= Yii::t('app', 'materials') ?></th>
                                        <th><?= Yii::t('app', 'total_sum') ?></th>
                                        <th><?= Yii::t('app', 'actions') ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($returnGroups as $group): ?>
                                        <tr>
                                            <td>
                                                <span class="badge badge-secondary">
                                                    <?= date('d.m.Y H:i', strtotime($group['created_at'] ?? 'now')) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <i class="fas fa-user"></i>
                                                <?= Html::encode($group['user_name'] ?? 'Неизвестный пользователь') ?>
                                            </td>
                                            <td>
                                                <i class="fas fa-truck"></i>
                                                <?= Html::encode($group['supplier_name'] ?? 'Неизвестный поставщик') ?>
                                            </td>
                                            <td>
                                                <div class="materials-list">
                                                    <?php if (is_array($group['materials']) && !empty($group['materials'])): ?>
                                                        <?php foreach ($group['materials'] as $material): ?>
                                                            <div class="material-item mb-1">
                                                                <span class="badge badge-light">
                                                                    <?= Html::encode($material['material_name'] ?? 'Неизвестный материал') ?>
                                                                </span>
                                                                <span class="text-muted">
                                                                    <?= number_format($material['quantity'] ?? 0, 2) ?>
                                                                    <?= Html::encode($material['unit_type_name'] ?? 'шт') ?>
                                                                </span>
                                                                <?php if (($material['unit_price'] ?? 0) > 0): ?>
                                                                    <span class="text-success">
                                                                        (<?= number_format($material['total_price'] ?? 0, 2) ?> сум)
                                                                    </span>
                                                                <?php endif; ?>
                                                            </div>
                                                        <?php endforeach; ?>
                                                    <?php else: ?>
                                                        <span class="text-muted">Нет материалов</span>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge badge-warning">
                                                    <?= number_format($group['total_sum'] ?? 0, 2) ?> сум
                                                </span>
                                            </td>
                                            <td>
                                                <button type="button"
                                                        class="btn btn-success btn-sm material-return-accept"
                                                        data-toggle="modal"
                                                        data-target="#ideal-mini-modal"
                                                        data-group-id="<?= $group['id'] ?? 0 ?>"
                                                        data-supplier="<?= Html::encode($group['supplier_name'] ?? 'Неизвестный поставщик') ?>">
                                                    <i class="fas fa-check"></i>
                                                    <?= Yii::t('app', 'accept') ?>
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>



<style>
.materials-list {
    max-width: 300px;
}

.material-item {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 5px;
}

.material-item .badge {
    font-size: 0.8em;
}

.table td {
    vertical-align: middle;
}

/* Стили для формы ввода цен в модальном окне */
.material-price-row {
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 15px;
    margin-bottom: 15px;
}

.material-price-row:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.price-input.is-invalid {
    border-color: #dc3545;
}

.material-total {
    color: #28a745;
    font-weight: bold;
}

#total-amount {
    color: #007bff;
    font-size: 1.1em;
}
</style>

<div id="confirm_text" data-text="<?= Yii::t('app', 'confirm_action') ?>"></div>

<?php
$js = <<<JS
(function($) {
    var confirmText = $('#confirm_text').attr('data-text');

    function initializeMaterialReturnAccept() {
        $(document).off('click.material-return-accept').on('click.material-return-accept', '.material-return-accept', function() {
            var groupId = $(this).data('group-id');
            var supplier = $(this).data('supplier');

            $.ajax({
                url: '/backend/material-return/get-price-form',
                dataType: 'json',
                type: 'GET',
                data: { group_id: groupId },
                success: function(response) {
                    if (response && response.content) {
                        $('#ideal-mini-modal .modal-title').html('Укажите цены для возврата материалов поставщику: ' + supplier);
                        $('#ideal-mini-modal .modal-body').html(response.content);
                        $('#ideal-mini-modal .mini-button').addClass("material-return-accept-button").attr('data-group-id', groupId);

                        // Инициализируем обработчики для полей ввода цен
                        initializePriceInputs();
                    }
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.material-return-accept-button').on('click.material-return-accept-button', '.material-return-accept-button', function() {
            if (!$(this).prop('disabled')) {
                var button = $(this);
                var groupId = button.attr('data-group-id');
                var originalText = button.html();

                // Собираем цены
                var prices = {};
                var isValid = true;

                $('.price-input').each(function() {
                    var materialId = $(this).data('material-id');
                    var price = parseFloat($(this).val());

                    if (!price || price <= 0) {
                        isValid = false;
                        $(this).addClass('is-invalid');
                    } else {
                        $(this).removeClass('is-invalid');
                        prices[materialId] = price;
                    }
                });

                if (!isValid) {
                    if (typeof iziToast !== 'undefined') {
                        iziToast.error({
                            timeout: 3000,
                            icon: 'fas fa-exclamation-triangle',
                            message: 'Пожалуйста, укажите корректные цены для всех материалов',
                            position: 'topRight'
                        });
                    } else {
                        alert('Пожалуйста, укажите корректные цены для всех материалов');
                    }
                    return;
                }

                button.prop('disabled', true);
                button.html('<i class="fas fa-spinner fa-spin"></i> Обработка...');

                $.ajax({
                    url: '/backend/material-return/accept',
                    dataType: 'json',
                    type: 'POST',
                    data: {
                        group_id: groupId,
                        material_prices: prices,
                        _csrf: $('meta[name=csrf-token]').attr('content')
                    },
                    success: function(response) {
                        if (response.status === 'success') {
                            if (typeof iziToast !== 'undefined') {
                                iziToast.success({
                                    timeout: 3000,
                                    icon: 'fas fa-check',
                                    message: response.message,
                                    position: 'topRight'
                                });
                            } else if (typeof toastr !== 'undefined') {
                                toastr.success(response.message);
                            } else {
                                alert(response.message);
                            }
                            setTimeout(function() {
                                location.reload();
                            }, 1500);
                        } else {
                            if (typeof iziToast !== 'undefined') {
                                iziToast.error({
                                    timeout: 5000,
                                    icon: 'fas fa-exclamation-triangle',
                                    message: response.message || 'Произошла ошибка',
                                    position: 'topRight'
                                });
                            } else if (typeof toastr !== 'undefined') {
                                toastr.error(response.message || 'Произошла ошибка');
                            } else {
                                alert(response.message || 'Произошла ошибка');
                            }
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        var errorMessage = 'Произошла ошибка при обработке запроса';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        }
                        if (typeof iziToast !== 'undefined') {
                            iziToast.error({
                                timeout: 5000,
                                icon: 'fas fa-exclamation-triangle',
                                message: errorMessage,
                                position: 'topRight'
                            });
                        } else if (typeof toastr !== 'undefined') {
                            toastr.error(errorMessage);
                        } else {
                            alert(errorMessage);
                        }
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                    },
                    complete: function() {
                        button.prop('disabled', false);
                        button.html(originalText);
                        $('.close').trigger('click');
                    }
                });
            }
        });
    }

    function initializePriceInputs() {
        // Пересчет общей суммы при изменении цен
        $(document).off('input.price-input').on('input.price-input', '.price-input', function() {
            var row = $(this).closest('.material-price-row');
            var price = parseFloat($(this).val()) || 0;
            var quantity = parseFloat($(this).data('quantity')) || 0;
            var total = price * quantity;

            row.find('.material-total').text(number_format(total, 2) + ' сум');
            calculateTotal();
        });
    }

    function calculateTotal() {
        var total = 0;
        $('.price-input').each(function() {
            var price = parseFloat($(this).val()) || 0;
            var quantity = parseFloat($(this).data('quantity')) || 0;
            total += price * quantity;
        });
        $('#total-amount').text(number_format(total, 2));
    }

    function number_format(number, decimals) {
        return parseFloat(number).toFixed(decimals).replace(/\B(?=(\d{3})+(?!\d))/g, " ");
    }

    $(document).ready(function() {
        initializeMaterialReturnAccept();
    });

})(jQuery);
JS;
$this->registerJs($js, \yii\web\View::POS_END);
?>