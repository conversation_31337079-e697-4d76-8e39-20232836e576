<?php
use yii\helpers\Html;
use yii\widgets\ActiveForm;
use app\assets\Select2Asset;
use app\common\models\EquipmentPart;

Select2Asset::register($this);
?>

<div class="part-form">
    <?php $form = ActiveForm::begin(['id' => 'part-create-form', 'options' => ['enctype' => 'multipart/form-data']]); ?>

    <div class="row">
        <div class="col-md-6">
            <div class="form-group">
                <label class="form-label"><?= Yii::t('app', 'Name') ?></label>
                <input type="text" class="form-control" name="name" required>
                <div class="invalid-feedback" id="name-error"></div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label class="form-label"><?= Yii::t('app', 'Photo') ?></label>
                <input type="file" class="form-control" name="photo" accept="image/*" required>
                <div class="invalid-feedback" id="photo-error"></div>
            </div>
        </div>
    </div>



    <div class="row mt-3">
        <div class="col-md-6">
            <div class="form-group">
                <label class="form-label"><?= Yii::t('app', 'Source Type') ?></label>
                <select class="form-control select2" name="source_type" required>
                    <?php foreach (EquipmentPart::getSourceTypeLabels() as $value => $label): ?>
                        <option value="<?= $value ?>"><?= $label ?></option>
                    <?php endforeach; ?>
                </select>
                <div class="invalid-feedback" id="source_type-error"></div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label class="form-label"><?= Yii::t('app', 'Status') ?></label>
                <select class="form-control select2" name="status" required>
                    <?php foreach (EquipmentPart::getStatusLabels() as $value => $label): ?>
                        <option value="<?= $value ?>"><?= $label ?></option>
                    <?php endforeach; ?>
                </select>
                <div class="invalid-feedback" id="status-error"></div>
            </div>
        </div>
    </div>

    <div class="row mt-3">
        <div class="col-md-6">
            <div class="form-group">
                <label class="form-label"><?= Yii::t('app', 'equipment') ?></label>
                <select class="form-control select2" name="equipment_id">
                    <option value=""><?= Yii::t('app', 'Not attached') ?></option>
                    <?php foreach ($equipments as $equipment): ?>
                        <option value="<?= $equipment->id ?>"><?= Html::encode($equipment->name) ?></option>
                    <?php endforeach; ?>
                </select>
                <div class="invalid-feedback" id="equipment_id-error"></div>
            </div>
        </div>
        <div class="col-md-6">
            <!-- Пустая колонка для выравнивания -->
        </div>
    </div>

    <div class="row mt-3" id="installation-date-container" style="display: none;">
        <div class="col-md-12">
            <div class="form-group">
                <label class="form-label"><?= Yii::t('app', 'Installation Date') ?></label>
                <input type="date" class="form-control" name="installation_date">
                <div class="invalid-feedback" id="installation_date-error"></div>
            </div>
        </div>
    </div>

    <div class="row mt-3">
        <div class="col-md-12">
            <div class="form-group">
                <label class="form-label"><?= Yii::t('app', 'comment') ?></label>
                <textarea class="form-control" name="comment" rows="3" style="min-height: 100px; resize: vertical;"></textarea>
                <div class="invalid-feedback" id="comment-error"></div>
            </div>
        </div>
    </div>

    <?php ActiveForm::end(); ?>
</div>

<style>
.invalid-feedback {
    display: block;
    margin-top: 5px;
}
</style>

<script>
    function restrictInput(event) {
        if (event.key === 'e' || event.key === 'E' || event.key === '-' || event.key === '+') {
            event.preventDefault();
        }
    }

    function validateAmount(input) {
        input.value = input.value.replace(/[^0-9.]/g, '');
        if (input.value.startsWith('0')) {
            input.value = '';
        }
    }

    $(document).ready(function() {
        // Показывать/скрывать поле даты установки в зависимости от выбора оборудования
        $('select[name="equipment_id"]').on('change', function() {
            if ($(this).val()) {
                $('#installation-date-container').show();
                $('input[name="installation_date"]').prop('required', true);
            } else {
                $('#installation-date-container').hide();
                $('input[name="installation_date"]').prop('required', false);
            }
        });

        // Инициализация при загрузке
        if ($('select[name="equipment_id"]').val()) {
            $('#installation-date-container').show();
            $('input[name="installation_date"]').prop('required', true);
        }
    });
</script>
