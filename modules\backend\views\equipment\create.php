<?php
use yii\helpers\Html;
use yii\widgets\ActiveForm;
use app\assets\Select2Asset;

Select2Asset::register($this);
?>

<div class="equipment-form">
    <?php $form = ActiveForm::begin(['id' => 'equipment-create-form', 'options' => ['enctype' => 'multipart/form-data']]); ?>

    <div class="row">
        <div class="col-md-6">
            <div class="form-group">
                <label class="form-label"><?= Yii::t('app', 'Name') ?></label>
                <input type="text" class="form-control" name="name" required>
                <div class="invalid-feedback" id="name-error"></div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label class="form-label"><?= Yii::t('app', 'Photo') ?></label>
                <input type="file" class="form-control" name="photo" accept="image/*">
                <div class="invalid-feedback" id="photo-error"></div>
            </div>
        </div>
    </div>

    <div class="row mt-3">
        <div class="col-md-6">
            <div class="form-group">
                <label class="form-label"><?= Yii::t('app', 'Purchase Date') ?></label>
                <input type="date" class="form-control" name="purchase_date" required>
                <div class="invalid-feedback" id="purchase_date-error"></div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label class="form-label"><?= Yii::t('app', 'Status') ?></label>
                <select class="form-control select2" name="status" required>
                    <option value=""><?= Yii::t('app', 'select_status') ?></option>
                    <option value="1"><?= Yii::t('app', 'active') ?></option>
                    <option value="0"><?= Yii::t('app', 'inactive') ?></option>
                    <option value="2"><?= Yii::t('app', 'repair') ?></option>
                </select>
                <div class="invalid-feedback" id="status-error"></div>
            </div>
        </div>
    </div>



    <div class="row mt-3">
        <div class="col-md-12">
            <div class="form-group">
                <label class="form-label"><?= Yii::t('app', 'Description') ?></label>
                <textarea class="form-control" name="description" rows="3" style="min-height: 100px; resize: vertical;"></textarea>
                <div class="invalid-feedback" id="description-error"></div>
            </div>
        </div>
    </div>

    <?php ActiveForm::end(); ?>
</div>

<style>
.invalid-feedback {
    display: none;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #dc3545;
}

.is-invalid ~ .invalid-feedback {
    display: block;
}

.form-control.is-invalid {
    border-color: #dc3545;
    padding-right: calc(1.5em + 0.75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23dc3545' viewBox='0 0 12 12'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}
</style>

<script>
    function restrictInput(event) {
        if (event.key === 'e' || event.key === 'E' || event.key === '-' || event.key === '+') {
            event.preventDefault();
        }
    }

    function validateAmount(input) {
        input.value = input.value.replace(/[^0-9.]/g, '');
        if (input.value.startsWith('0')) {
            input.value = '';
        }
    }
</script>