<?php

namespace app\common\models;

use Yii;

/**
 * This is the model class for table "equipment".
 *
 * @property int $id
 * @property string $name
 * @property string|null $photo
 * @property string|null $description
 * @property string|null $purchase_date
 * @property float $initial_cost
 * @property int $useful_life_years
 * @property string $status
 * @property string|null $created_at
 * @property string|null $deleted_at
 */
class Equipment extends \yii\db\ActiveRecord
{
    const SCENARIO_CREATE = 'create';
    const SCENARIO_CHANGE_STATUS = 'change_status';


    const STATUS_ACTIVE = 1;
    const STATUS_INACTIVE = 0;
    const STATUS_REPAIR = 2;

    public static function getStatusLabels()
    {
        return [
            self::STATUS_ACTIVE => Yii::t('app', 'active'),
            self::STATUS_INACTIVE => Yii::t('app', 'inactive'),
            self::STATUS_REPAIR => Yii::t('app', 'repair'),
        ];
    }

    public static function getStatusLabel(int $status)
    {
        return self::getStatusLabels()[$status] ?? 'Noma`lum';
    }
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'equipment';
    }

    public function scenarios()
    {
        $scenarios = parent::scenarios();
        $scenarios[self::SCENARIO_CREATE] = ['name', 'photo', 'description', 'purchase_date', 'status'];
        $scenarios[self::SCENARIO_CHANGE_STATUS] = ['status'];
        return $scenarios;
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['name', 'photo', 'purchase_date', 'status'], 'required', 'on' => self::SCENARIO_CREATE],
            [['status'], 'required', 'on' => self::SCENARIO_CHANGE_STATUS],
            [['description'], 'string'],
            [['purchase_date', 'created_at', 'deleted_at'], 'safe'],
            [['name', 'photo'], 'string', 'max' => 255],
            [['status'], 'string', 'max' => 50],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'name' => Yii::t('app', 'equipment_name'),
            'photo' => Yii::t('app', 'equipment_photo'),
            'description' => Yii::t('app', 'equipment_description'),
            'purchase_date' => Yii::t('app', 'purchase_date'),
            'initial_cost' => Yii::t('app', 'initial_cost'),
            'useful_life_years' => Yii::t('app', 'useful_life_years'),
            'status' => Yii::t('app', 'status'),
            'created_at' => Yii::t('app', 'created_at'),
            'deleted_at' => Yii::t('app', 'deleted_at'),
        ];
    }

    /**
     * Gets query for [[PartAssignments]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getPartAssignments()
    {
        return $this->hasMany(EquipmentPartAssignment::class, ['equipment_id' => 'id']);
    }

    /**
     * Gets query for active part assignments.
     *
     * @return \yii\db\ActiveQuery
     */
    public function getActivePartAssignments()
    {
        return $this->hasMany(EquipmentPartAssignment::class, ['equipment_id' => 'id'])
            ->where(['status' => EquipmentPartAssignment::STATUS_ACTIVE]);
    }

    /**
     * Gets query for assigned parts through assignments.
     *
     * @return \yii\db\ActiveQuery
     */
    public function getAssignedParts()
    {
        return $this->hasMany(EquipmentPart::class, ['id' => 'equipment_part_id'])
            ->via('activePartAssignments');
    }

    /**
     * Get total count of assigned parts
     * @return int
     */
    public function getAssignedPartsCount()
    {
        return $this->getActivePartAssignments()->count();
    }
}
