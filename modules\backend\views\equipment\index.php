<?php

use app\common\models\Equipment;
use yii\helpers\Html;
use yii\widgets\Pjax;
use yii\web\View;
use app\assets\DataTablesAsset;

DataTablesAsset::register($this);

$this->title = Yii::t("app", "Equipments");
$this->params['breadcrumbs'][] = $this->title;

$searchLabel = Yii::t("app", "search:");
$lengthMenuLabel = Yii::t("app", "Show _MENU_ entries");
$zeroRecordsLabel = Yii::t("app", "Nothing found");
$infoLabel = Yii::t("app", "Showing _PAGE_ to _PAGES_ of _MAX_ items");
$infoEmptyLabel = Yii::t("app", "Nothing found");
$infoFilteredLabel = Yii::t("app", "(filtered from _MAX_ records)");
$all = Yii::t("app", "all");
$active = Yii::t("app", "active");
$inactive = Yii::t("app", "inactive");
$repair = Yii::t("app", "repair");
$equipment = [];
$equipment = Equipment::getStatusLabels();
?>

<style>
    #equipment_filter.select2 {
        min-width: 145px !important;
        width: 100% !important;
    }

    /* Стили для контейнера Select2 (если используется библиотека Select2) */
    #equipment_filter + .select2-container {
        width: 145px !important;
    }

    /* Стили для полей ввода даты */
    input[type="date"].form-control {
        width: 150px;
    }

    /* Отступы между элементами */
    .d-flex.gap-2 {
        gap: 0.5rem !important;
    }
    .select_type_status
</style>

<div class="card-body">
    <div class="row align-items-center mb-3">
        <div class="col-md-6">
            <h4 class="my-0"><?= Html::encode($this->title) ?></h4>
        </div>
        <div class="col-md-6 text-right">
            <div class="d-flex justify-content-end align-items-center gap-2">

                    <select id="equipment_filter" class="form-control select2">
                        <option value=""><?= Yii::t('app', 'select_type_status') ?></option>
                        <?php foreach($equipment as $index => $value): ?>
                           <option value="<?= $index ?>" style="text-align: left;"><?= $value ?></option>
                        <?php endforeach; ?>
                    </select>
                        <!-- Filter button -->
                        <button type="button" class="btn btn-primary" id="search-button">
                            <?= Yii::t('app', 'search') ?>
                        </button>

                <?php if (Yii::$app->user->can('admin')): ?>
                    <a href="#" class="btn btn-primary equipment-create" data-toggle="modal" data-target="#ideal-mini-modal">
                        <?= Yii::t("app", "Create Equipment") ?>
                    </a>
                <?php endif ?>
            </div>
        </div>
    </div>

    <?php Pjax::begin(['id' => 'equipment-grid-pjax']); ?>
    <?php if($result): ?>
        <div>
            <table id="equipment-grid-view" class="table table-bordered table-striped compact">
                <thead>
                    <th><?= Yii::t("app", "Photo") ?></th>
                    <th><?= Yii::t("app", "Name") ?></th>
                    <th><?= Yii::t("app", "Description") ?></th>
                    <th><?= Yii::t("app", "Status") ?></th>
                    <th><?= Yii::t("app", "Purchase Date") ?></th>
                    <th><?= Yii::t("app", "actions") ?></th>
                </thead>
                <tbody>
                <?php foreach ($result as $model): ?>
                    <tr class="equipment-row" data-equipment-id="<?= $model['id'] ?>" style="cursor: pointer;">
                        <td>
                            <?php if ($model['photo']): ?>
                                <img src="/uploads/equipment/<?= Html::encode($model['photo']) ?>"
                                     alt="Equipment"
                                     style="max-width: 50px; cursor: pointer;"
                                     class="equipment-photo"
                                     data-id="<?= $model['id'] ?>">
                            <?php endif; ?>
                        </td>
                        <td><?= Html::encode($model['name']) ?></td>
                        <td><?= Html::encode($model['description']) ?></td>
                        <td>
                            <span class="badge badge-<?= $model['status'] == 0 ? 'danger' : ($model['status'] == 1 ? 'success' : 'warning') ?>">
                                <?= $model['status'] == 0 ? Yii::t("app", "inactive") : ($model['status'] == 1 ? Yii::t("app", "active") : Yii::t("app", "repair")) ?>
                            </span>
                        </td>
                        <td data-order="<?= strtotime($model['purchase_date']) ?>">
                            <?= Yii::$app->formatter->asDate($model['purchase_date']) ?>
                        </td>
                        <td>
                            <div class="dropdown d-inline">
                                <a href="#" class="badge badge-info dropdown-toggle" data-toggle="dropdown">
                                    <?php echo Yii::t("app", "detail"); ?>
                                </a>
                                <div class="dropdown-menu">
                                    <?php if ($model['deleted_at'] == NULL): ?>

                                        <a href="#" class="dropdown-item equipment-update" data-toggle="modal" data-target="#ideal-mini-modal" data-id="<?= Html::encode($model['id']) ?>">
                                            <?= Yii::t("app", "edit") ?>
                                        </a>

                                        <a href="#" class="dropdown-item equipment-change-status" data-toggle="modal" data-target="#ideal-mini-modal" data-id="<?= Html::encode($model['id']) ?>">
                                            <?= Yii::t("app", "change_status") ?>
                                        </a>


                                    <?php endif; ?>
                                </div>
                            </div>
                        </td>
                    </tr>
                <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <p><?= Yii::t('app', 'no_data_available') ?></p>
    <?php endif; ?>
    <?php Pjax::end(); ?>
</div>


<div id="one" data-text="<?= Yii::t("app", "Create Equipment") ?>"></div>
<div id="two" data-text="<?= Yii::t("app", "change_status") ?>"></div>



<style>
.equipment-row:hover {
    background-color: #f8f9fa !important;
    transition: background-color 0.2s ease;
}

.equipment-row:hover td {
    background-color: transparent !important;
}

.equipment-row {
    transition: background-color 0.2s ease;
}
</style>

<?php
$js = <<<JS
(function($) {
    var one = $('#one').attr('data-text');
    var two = $('#two').attr('data-text');
    var all = "{$all}";
    var active = "{$active}";
    var inactive = "{$inactive}";
    var repair = "{$repair}";
    var detailText = "<?= Yii::t('app', 'detail') ?>";
    var editText = "<?= Yii::t('app', 'edit') ?>";
    var changeStatusText = "<?= Yii::t('app', 'change_status') ?>";

    var searchLabel = "{$searchLabel}";
    var lengthMenuLabel = "{$lengthMenuLabel}";
    var zeroRecordsLabel = "{$zeroRecordsLabel}";
    var infoLabel = "{$infoLabel}";
    var infoEmptyLabel = "{$infoEmptyLabel}";
    var infoFilteredLabel = "{$infoFilteredLabel}";

    function initializeDataTable() {
        if ($.fn.DataTable.isDataTable('#equipment-grid-view')) {
            $('#equipment-grid-view').DataTable().destroy();
        }

        $('#equipment-grid-view').DataTable({
            "language": {
                "search": searchLabel,
                "lengthMenu": lengthMenuLabel,
                "zeroRecords": zeroRecordsLabel,
                "info": infoLabel,
                "infoEmpty": infoEmptyLabel,
                "infoFiltered": infoFilteredLabel
            },
            "pageLength": 50,
            "columnDefs": [
                {
                    "targets": [0, 5],
                    "orderable": false
                },
                {
                    "targets": 4,
                    "render": function(data, type, row) {
                        if (type === 'display' && data) {
                            return moment(data).format('DD.MM.YYYY');
                        }
                        return data;
                    }
                }
            ]
        });
    }


    function initializeSearch() {
        $('#search-button').on('click', function() {
            var status = $('#equipment_filter').val();

            $.ajax({
                url: '/backend/equipment/search',
                type: 'POST',
                data: {
                    status: status
                },
                success: function(response) {
                    if (response.status === 'success') {
                        $('#equipment-grid-pjax').html(response.content);
                        initializeDataTable();
                    } else {
                        alert(response.message);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Search error:', error);
                    alert("<?= Yii::t('app', 'Error occurred while searching') ?>");
                }
            });
        });
    }

    function initializeSelect2() {
        $('.select2:not([multiple])').select2({
            width: '100%',
            language: {
                noResults: function() {
                    return "<?= Yii::t('app', 'No results found') ?>";
                },
                searching: function() {
                    return "<?= Yii::t('app', 'Searching...') ?>";
                }
            }
        });

        // Инициализация мультиселекта
        $('.select2[multiple]').select2({
            width: '100%',
            language: {
                noResults: function() {
                    return "<?= Yii::t('app', 'No results found') ?>";
                },
                searching: function() {
                    return "<?= Yii::t('app', 'Searching...') ?>";
                }
            },
            placeholder: "<?= Yii::t('app', 'Select parts') ?>",
            allowClear: true
        });
    }

    function initializeEquipmentDecommission() {
        $(document).off('click.equipment-decommission').on('click.equipment-decommission', '.equipment-decommission', function() {
            var id = $(this).data('id');
            $.ajax({
                url: '/backend/equipment/decommission',
                type: 'GET',
                data: {id: id},
                success: function(response) {
                    $('#ideal-mini-modal .modal-title').html("<?= Yii::t('app', 'decommission') ?>");
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass('equipment-decommission-save');
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.equipment-decommission-save').on('click.equipment-decommission-save', '.equipment-decommission-save', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var id = $('.equipment-decommission').data('id');
                var formData = new FormData($('#equipment-decommission-form')[0]);

                $.ajax({
                    url: '/backend/equipment/decommission?id=' + id,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({
                                container: '#equipment-grid-pjax',
                                complete: function() {
                                    initializeDataTable();
                                }
                            });
                            alert(response.message);
                        } else {
                            button.prop('disabled', false);
                            alert(response.message);
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        button.prop('disabled', false);
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                    }
                });
            }
        });
    }

    function initializeActionButtons() {
        // Кнопка "Архивировать"
        $('#btn-archive').on('click', function(e) {
            e.preventDefault();
            alert('<?= Yii::t("app", "Archive functionality will be implemented in the future") ?>');
        });

        // Кнопка "Поделиться"
        $('#btn-share').on('click', function(e) {
            e.preventDefault();
            alert('<?= Yii::t("app", "Share functionality will be implemented in the future") ?>');
        });
    }

    function initializeEquipmentPartsTab() {
        $(document).off('click.equipment-parts-tab').on('click.equipment-parts-tab', '.equipment-parts-tab', function() {
            var id = $(this).data('id');
            $.ajax({
                url: '/backend/equipment/parts-tab',
                type: 'GET',
                data: {id: id},
                success: function(response) {
                    $('#ideal-large-modal-without-save .modal-title').html("<?= Yii::t('app', 'Equipment Parts') ?>");
                    $('#ideal-large-modal-without-save .modal-body').html(response.content);
                    $('#ideal-large-modal-without-save').modal('show');

                    // Инициализируем обработчики для запчастей внутри вкладки
                    initializePartUpdate();
                    initializePartPhoto();
                    initializePartStatus();
                    initializePartHistory();
                    initializePartDetach();
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });
    }

    function initializeAll() {
        initializeDataTable();
        initializeSelect2();
        initializeEquipmentCreate();
        initializeEquipmentUpdate();
        initializeEquipmentPhoto();
        initializeEquipmentStatus();
        initializeEquipmentDecommission();
        initializeEquipmentPartsTab();
        initializeActionButtons();
        initializeSearch();
        initializeDropdown();
    }

    // Initialize everything on first load
    initializeAll();

    // Re-initialize after PJAX reloads
    $(document).on('pjax:success', function() {
        initializeAll();
    });

    function initializeDropdown() {
        $(document).off('click.dropdown').on('click.dropdown', '.dropdown-toggle', function(e) {
            e.preventDefault();
            e.stopPropagation();
            var dropdownMenu = $(this).siblings('.dropdown-menu');
            $('.dropdown-menu').not(dropdownMenu).removeClass('show');
            dropdownMenu.toggleClass('show');
        });

        $(document).off('click.dropdown-item').on('click.dropdown-item', '.dropdown-item:not(.spare-parts-link)', function(e) {
            e.preventDefault();
            e.stopPropagation();
        });

        // Специальный обработчик для кнопки запчастей
        $(document).off('click.spare-parts-link').on('click.spare-parts-link', '.spare-parts-link', function(e) {
            e.preventDefault();
            e.stopPropagation();
            var url = $(this).data('url');
            console.log('Navigating to URL:', url);
            window.location.href = url;
        });

        $(document).off('click.dropdown-close').on('click.dropdown-close', function(e) {
            if (!$(e.target).closest('.dropdown').length) {
                $('.dropdown-menu').removeClass('show');
            }
        });
    }

    function initializeEquipmentCreate() {
        $(document).off('click.equipment-create').on('click.equipment-create', '.equipment-create', function() {
            $.ajax({
                url: '/backend/equipment/create',
                dataType: 'json',
                type: 'GET',
                success: function(response) {
                    $('#ideal-mini-modal .modal-title').html(one);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass("equipment-create-button");
                    initializeSelect2();
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.equipment-create-button').on('click.equipment-create-button', '.equipment-create-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = new FormData($('#equipment-create-form')[0]);
                $.ajax({
                    url: '/backend/equipment/create',
                    dataType: 'json',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response && response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({
                                container: '#equipment-grid-pjax',
                                complete: function() {
                                    initializeDataTable();
                                }
                            });
                        } else if (response && response.status === 'error') {
                            button.prop('disabled', false);
                            // Очистка предыдущих ошибок
                            $('.form-control').removeClass('is-invalid');
                            $('.invalid-feedback').text('').hide();

                            // Отображение новых ошибок
                            $.each(response.errors, function(field, errors) {
                                var input = $('[name="' + field + '"]');
                                var errorDiv = $('#' + field + '-error');

                                input.addClass('is-invalid');
                                errorDiv.text(errors.join(', ')).show();
                            });
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        button.prop('disabled', false);
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                    }
                });
            }
        });
    }



    function initializeEquipmentUpdate() {
        $(document).off('click.equipment-update').on('click.equipment-update', '.equipment-update', function() {
            var id = $(this).data('id');
            $.ajax({
                url: '/backend/equipment/update',
                dataType: 'json',
                type: 'GET',
                data: {id: id},
                success: function(response) {
                    $('#ideal-mini-modal .modal-title').html(one);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass("equipment-update-button");
                    initializeSelect2();
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.equipment-update-button').on('click.equipment-update-button', '.equipment-update-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = new FormData($('#equipment-update-form')[0]);
                $.ajax({
                    url: '/backend/equipment/update',
                    dataType: 'json',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response && response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({
                                container: '#equipment-grid-pjax',
                                complete: function() {
                                    initializeDataTable();
                                }
                            });
                        } else if (response && response.status === 'error') {
                            button.prop('disabled', false);
                            // Очистка предыдущих ошибок
                            $('.form-control').removeClass('is-invalid');
                            $('.invalid-feedback').text('').hide();

                            // Отображение новых ошибок
                            $.each(response.errors, function(field, errors) {
                                var input = $('[name="' + field + '"]');
                                var errorDiv = $('#' + field + '-error');

                                input.addClass('is-invalid');
                                errorDiv.text(errors.join(', ')).show();
                            });
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        button.prop('disabled', false);
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                    }
                });
            }
        });
    }






    function initializeEquipmentPhoto() {
        $(document).off('click.equipment-photo');

        $(document).on('click.equipment-photo', '.equipment-photo', function() {
            var id = $(this).data('id');
            $.ajax({
                url: '/backend/equipment/get-photo',
                type: 'GET',
                data: {id: id},
                success: function(response) {
                    if (response.status === 'success') {
                        $('#ideal-large-modal-without-save .modal-title').text(response.name);
                        var content = '<div class="text-center"><img src="' + response.photo + '" alt="" style="max-width: 100%;"></div>';
                        $('#ideal-large-modal-without-save .modal-body').html(content);
                        $('#ideal-large-modal-without-save').modal('show');
                    } else {
                        alert(response.message);
                    }
                },
                error: function() {
                    alert("<?= Yii::t('app', 'Error loading photo') ?>");
                }
            });
        });

        // Очистка содержимого при закрытии
        $('#ideal-large-modal-without-save').on('hidden.bs.modal', function () {
            $('#ideal-large-modal-without-save .modal-body').html('');
            $('#ideal-large-modal-without-save .modal-title').text('');
        });
    }

    function initializeEquipmentStatus() {
        $(document).off('click.equipment-change-status').on('click.equipment-change-status', '.equipment-change-status', function() {
            var id = $(this).data('id');
            $.ajax({
                url: '/backend/equipment/change-status',
                type: 'GET',
                data: {id: id},
                success: function(response) {
                    $('#ideal-mini-modal .modal-title').html(two);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass('equipment-status-save');
                    initializeSelect2();
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.equipment-status-save').on('click.equipment-status-save', '.equipment-status-save', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var id = $('.equipment-change-status').data('id');
                var formData = new FormData($('#equipment-status-form')[0]);

                // Проверяем, выбран ли статус "Списать" (STATUS_INACTIVE = 0)
                var selectedStatus = $('#equipment-status').val();

                // Если выбран статус "Списать", собираем выбранные запчасти
                if (selectedStatus == '0') {
                    // Получаем выбранные запчасти через чекбоксы
                    var selectedParts = [];
                    $('#parts-selection-block input[type="checkbox"]:checked').each(function() {
                        selectedParts.push($(this).val());
                    });

                    // Добавляем выбранные запчасти в formData
                    selectedParts.forEach(function(partId) {
                        formData.append('parts[]', partId);
                    });

                }

                $.ajax({
                    url: '/backend/equipment/change-status?id=' + id,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({
                                container: '#equipment-grid-pjax',
                                complete: function() {
                                    initializeDataTable();
                                }
                            });
                        } else {
                            button.prop('disabled', false);
                            alert(response.message);
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        button.prop('disabled', false);
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                    }
                });
            }
        });

        // Обработчик клика по строкам таблицы для перехода к запчастям
        $(document).off('click.equipment-row').on('click.equipment-row', '.equipment-row', function(e) {
            // Проверяем, что клик не был по dropdown или его элементам
            if (!$(e.target).closest('.dropdown').length &&
                !$(e.target).hasClass('dropdown-toggle') &&
                !$(e.target).hasClass('dropdown-item') &&
                !$(e.target).closest('.equipment-photo').length) {

                var equipmentId = $(this).data('equipment-id');
                if (equipmentId) {
                    window.location.href = '/backend/equipment/' + equipmentId + '/parts';
                }
            }
        });

        // Предотвращаем переход при клике на dropdown
        $(document).off('click.dropdown-prevent').on('click.dropdown-prevent', '.dropdown, .dropdown-toggle, .dropdown-item', function(e) {
            e.stopPropagation();
        });

        // Предотвращаем переход при клике на фото (для увеличения)
        $(document).off('click.photo-prevent').on('click.photo-prevent', '.equipment-photo', function(e) {
            e.stopPropagation();
        });
    }
})(jQuery);
JS;
$this->registerJs($js, View::POS_END);
?>
