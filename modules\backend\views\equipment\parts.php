<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\widgets\Pjax;
use yii\web\View;
use app\common\models\EquipmentPart;
use app\assets\DataTablesAsset;

DataTablesAsset::register($this);

$this->title = Yii::t('app', 'equipment_parts') . ': ' . Html::encode($model->name);
$this->params['breadcrumbs'][] = ['label' => Yii::t('app', 'equipment'), 'url' => ['index']];
$this->params['breadcrumbs'][] = ['label' => Html::encode($model->name), 'url' => ['view', 'id' => $model->id]];
$this->params['breadcrumbs'][] = Yii::t('app', 'parts');

// Переменные для JavaScript
$all = Yii::t('app', 'all');
$active = Yii::t('app', 'active');
$inactive = Yii::t('app', 'inactive');
$reserve = Yii::t('app', 'reserve');

$searchLabel = Yii::t("app", "search:");
$lengthMenuLabel = Yii::t("app", "Show _MENU_ entries");
$zeroRecordsLabel = Yii::t("app", "Nothing found");
$infoLabel = Yii::t("app", "Showing _PAGE_ to _PAGES_ of _MAX_ items");
$infoEmptyLabel = Yii::t("app", "Nothing found");
$infoFilteredLabel = Yii::t("app", "(filtered from _MAX_ records)");
?>

<style>
    #parts_filter.select2 {
        min-width: 145px !important;
        width: 100% !important;
    }

    /* Стили для контейнера Select2 */
    #parts_filter + .select2-container {
        width: 145px !important;
    }

    /* Отступы между элементами */
    .d-flex.gap-2 {
        gap: 0.5rem !important;
    }

    /* Исправление z-index для модальных окон */
    .modal {
        z-index: 1050 !important;
    }

    .modal-backdrop {
        z-index: 1040 !important;
    }

    /* Исправление z-index для Select2 в модальных окнах */
    .select2-container {
        z-index: 1060 !important;
    }

    .select2-dropdown {
        z-index: 1060 !important;
    }

    /* Стили для формы добавления запчасти */
    #part-info {
        border-left: 4px solid #17a2b8;
        background-color: #d1ecf1;
        border-color: #bee5eb;
    }

    #quantity-help {
        font-size: 0.875em;
        color: #6c757d;
    }
</style>

<div class="card-body">
    <div class="row align-items-center mb-3">
        <div class="col-md-6">
            <h4 class="my-0"><?= Yii::t('app', 'equipment_parts') ?>: <?= Html::encode($model->name) ?></h4>
        </div>
        <div class="col-md-6 text-right">
            <div class="d-flex justify-content-end align-items-center gap-2">

                <select class="form-control select2" id="parts_filter" name="status">
                    <option value=""><?= Yii::t('app', 'all') ?></option>
                    <option value="<?= EquipmentPart::STATUS_ACTIVE ?>"><?= Yii::t('app', 'active') ?></option>
                    <option value="<?= EquipmentPart::STATUS_RESERVE ?>"><?= Yii::t('app', 'reserve') ?></option>
                    <option value="<?= EquipmentPart::STATUS_INACTIVE ?>"><?= Yii::t('app', 'inactive') ?></option>
                </select>

                <input type="text" class="form-control" id="parts_search" placeholder="<?= Yii::t('app', 'enter_part_name') ?>" style="width: 200px;">

                <button type="button" class="btn btn-primary" id="search-parts-button">
                    <?= Yii::t('app', 'search') ?>
                </button>


                <a href="<?= Url::to(['index']) ?>" class="btn btn-success">
                    <?= Yii::t('app', 'back_to_equipment') ?>
                </a>

                <?php if (Yii::$app->user->can('admin')): ?>
                    <a href="#" class="btn btn-primary part-create" data-toggle="modal" data-target="#ideal-mini-modal" data-equipment-id="<?= $model->id ?>">
                        <?= Yii::t('app', 'add_part') ?>
                    </a>
                <?php endif ?>
            </div>
        </div>
    </div>

    <?php Pjax::begin(['id' => 'parts-grid-pjax']); ?>
    <?php if($result): ?>
        <div>
            <table id="parts-grid-view" class="table table-bordered table-striped compact">
                <thead>
                    <th><?= Yii::t("app", "Photo") ?></th>
                    <th><?= Yii::t("app", "Name") ?></th>
                    <th><?= Yii::t("app", "Quantity") ?></th>
                    <th><?= Yii::t("app", "installation_date") ?></th>
                    <th><?= Yii::t("app", "Price") ?></th>
                    <th><?= Yii::t("app", "Status") ?></th>
                    <th><?= Yii::t("app", "comment") ?></th>
                    <th><?= Yii::t("app", "actions") ?></th>
                </thead>
                <tbody>
                <?php foreach ($result as $part): ?>
                    <tr class="clickable-row" data-href="<?= Url::to(['part-history', 'equipmentId' => $model->id, 'partId' => $part['id']]) ?>" style="cursor: pointer;">
                        <td>
                            <?php if ($part['photo']): ?>
                                <img src="/uploads/equipment_parts/<?= Html::encode($part['photo']) ?>"
                                     alt="Part"
                                     style="max-width: 50px; cursor: pointer;"
                                     class="part-photo"
                                     data-id="<?= $part['id'] ?>">
                            <?php endif; ?>
                        </td>
                        <td><?= Html::encode($part['name']) ?></td>
                        <td>
                            <?php if (($part['active_quantity'] ?? 0) > 0): ?>
                                <span class="badge badge-success mr-1">
                                    <?= Yii::t('app', 'active') ?>: <?= $part['active_quantity'] ?>
                                </span>
                            <?php endif; ?>
                            <?php if (($part['repair_quantity'] ?? 0) > 0): ?>
                                <span class="badge badge-warning">
                                    <?= Yii::t('app', 'repair') ?>: <?= $part['repair_quantity'] ?>
                                </span>
                            <?php endif; ?>
                            <?php if (($part['active_quantity'] ?? 0) == 0 && ($part['repair_quantity'] ?? 0) == 0): ?>
                                <span class="badge badge-secondary">0</span>
                            <?php endif; ?>
                        </td>
                        <td data-order="<?= $part['installation_date'] ? strtotime($part['installation_date']) : 0 ?>">
                            <?php if ($part['installation_date']): ?>
                                <?= date('d.m.Y', strtotime($part['installation_date'])) ?>
                            <?php else: ?>
                                <span class="text-muted"><?= Yii::t('app', 'not_installed') ?></span>
                            <?php endif; ?>
                        </td>
                        <td><?= $part['price'] ? Yii::$app->formatter->asDecimal($part['price'], 2) : '0.00' ?></td>
                        <td>
                            <span class="badge badge-<?= $part['status'] == EquipmentPart::STATUS_INACTIVE ? 'danger' : ($part['status'] == EquipmentPart::STATUS_ACTIVE ? 'success' : 'warning') ?>">
                                <?= $part['status'] == EquipmentPart::STATUS_INACTIVE ? Yii::t("app", "inactive") : ($part['status'] == EquipmentPart::STATUS_ACTIVE ? Yii::t("app", "active") : Yii::t("app", "reserve")) ?>
                            </span>
                        </td>
                        <td><?= Html::encode($part['comment'] ?? '-') ?></td>
                        <td>
                            <div class="dropdown d-inline">
                                <a href="#" class="badge badge-info dropdown-toggle" data-toggle="dropdown">
                                    <?php echo Yii::t("app", "detail"); ?>
                                </a>
                                <div class="dropdown-menu">
                                    <?php if ($part['deleted_at'] == NULL): ?>

                                        <a href="<?= Url::to(['/backend/equipment-part/history', 'id' => $part['id']]) ?>" class="dropdown-item">
                                            <?= Yii::t("app", "history") ?>
                                        </a>

                                        <?php if (($part['active_quantity'] ?? 0) > 0): ?>
                                            <a href="#" class="dropdown-item equipment-part-reserve" data-toggle="modal" data-target="#ideal-mini-modal" data-equipment-id="<?= $model->id ?>" data-part-id="<?= Html::encode($part['id']) ?>">
                                                <?= Yii::t("app", "move_to_reserve") ?>
                                            </a>

                                            <a href="#" class="dropdown-item equipment-part-repair" data-toggle="modal" data-target="#ideal-mini-modal" data-equipment-id="<?= $model->id ?>" data-part-id="<?= Html::encode($part['id']) ?>">
                                                <?= Yii::t("app", "send_to_repair") ?>
                                            </a>
                                        <?php endif; ?>

                                        <?php if (($part['repair_quantity'] ?? 0) > 0): ?>
                                            <a href="#" class="dropdown-item equipment-part-activate" data-toggle="modal" data-target="#ideal-mini-modal" data-equipment-id="<?= $model->id ?>" data-part-id="<?= Html::encode($part['id']) ?>">
                                                <?= Yii::t("app", "return_to_work") ?>
                                            </a>
                                        <?php endif; ?>

                                        <a href="#" class="dropdown-item equipment-part-defect" data-toggle="modal" data-target="#ideal-mini-modal" data-equipment-id="<?= $model->id ?>" data-part-id="<?= Html::encode($part['id']) ?>">
                                            <?= Yii::t("app", "defect") ?>
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </td>
                    </tr>
                <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <p><?= Yii::t('app', 'no_parts_for_equipment') ?></p>
    <?php endif; ?>
    <?php Pjax::end(); ?>
</div>



<div id="one" data-text="<?= Yii::t("app", "add_part") ?>"></div>
<div id="three" data-text="<?= Yii::t("app", "send_to_defect") ?>"></div>
<div id="four" data-text="<?= Yii::t("app", "move_to_reserve") ?>"></div>
<div id="five" data-text="<?= Yii::t("app", "send_to_repair") ?>"></div>
<div id="six" data-text="<?= Yii::t("app", "return_to_work") ?>"></div>


<?php
$js = <<<JS
(function($) {
    var equipmentId = {$model->id};
    var searchLabel = "{$searchLabel}";
    var lengthMenuLabel = "{$lengthMenuLabel}";
    var zeroRecordsLabel = "{$zeroRecordsLabel}";
    var infoLabel = "{$infoLabel}";
    var infoEmptyLabel = "{$infoEmptyLabel}";
    var infoFilteredLabel = "{$infoFilteredLabel}";

    var one = $('#one').data('text');
    var three = $('#three').data('text');
    var four = $('#four').data('text');
    var five = $('#five').data('text');
    var six = $('#six').data('text');

    function initializeDataTable() {
        if ($.fn.DataTable.isDataTable('#parts-grid-view')) {
            $('#parts-grid-view').DataTable().destroy();
        }

        $('#parts-grid-view').DataTable({
            "language": {
                "search": searchLabel,
                "lengthMenu": lengthMenuLabel,
                "zeroRecords": zeroRecordsLabel,
                "info": infoLabel,
                "infoEmpty": infoEmptyLabel,
                "infoFiltered": infoFilteredLabel
            },
            "pageLength": 50,
            "columnDefs": [
                {
                    "targets": [0, 7],
                    "orderable": false
                },
                {
                    "targets": 3,
                    "render": function(data, type, row) {
                        if (type === 'display' && data && data !== '-') {
                            return moment(data).format('DD.MM.YYYY');
                        }
                        return data;
                    }
                }
            ]
        });
    }

    function initializeSelect2() {
        $('.select2:not([multiple])').select2({
            width: '100%',
            language: {
                noResults: function() {
                    return "<?= Yii::t('app', 'No results found') ?>";
                },
                searching: function() {
                    return "<?= Yii::t('app', 'Searching...') ?>";
                }
            }
        });
    }

    function initializeSearch() {
        $('#search-parts-button').on('click', function() {
            var status = $('#parts_filter').val();
            var search = $('#parts_search').val();

            $.ajax({
                url: '/backend/equipment/search-parts/' + equipmentId,
                type: 'POST',
                data: {
                    status: status,
                    search: search
                },
                success: function(response) {
                    if (response.status === 'success') {
                        $('#parts-grid-pjax').html(response.content);
                        initializeDataTable();
                    } else {
                        alert(response.message);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Search error:', error);
                    alert("<?= Yii::t('app', 'Error occurred while searching') ?>");
                }
            });
        });

        $('#reset-parts-button').on('click', function() {
            $('#parts_filter').val('').trigger('change');
            $('#parts_search').val('');
            $('#search-parts-button').trigger('click');
        });

        // Поиск по Enter
        $('#parts_search').on('keypress', function(e) {
            if (e.which === 13) {
                $('#search-parts-button').trigger('click');
            }
        });
    }

    function initializePartCreate() {
        $(document).off('click.part-create').on('click.part-create', '.part-create', function() {
            var equipmentId = $(this).data('equipment-id');
            $.ajax({
                url: '/backend/equipment/add-part-to-equipment/' + equipmentId,
                dataType: 'json',
                type: 'GET',
                success: function(response) {
                    $('#ideal-mini-modal .modal-title').html(one);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass("part-create-button");
                    initializeSelect2();
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.part-create-button').on('click.part-create-button', '.part-create-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var equipmentId = $('.part-create').data('equipment-id');
                var formData = new FormData($('#add-part-form')[0]);

                $.ajax({
                    url: '/backend/equipment/add-part-to-equipment/' + equipmentId,
                    dataType: 'json',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response && response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({
                                container: '#parts-grid-pjax',
                                complete: function() {
                                    initializeDataTable();
                                }
                            });
                            } else if (response && response.status === 'error') {
                            button.prop('disabled', false);
                            $('.form-control').removeClass('is-invalid');
                            $('.invalid-feedback').text('').hide();

                            if (response.errors) {
                                $.each(response.errors, function(field, errors) {
                                    var input = $('[name="' + field + '"]');
                                    var errorDiv = $('#' + field + '-error');

                                    input.addClass('is-invalid');
                                    errorDiv.text(errors.join(', ')).show();
                                });
                            } else {
                                alert(response.message);
                            }
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        button.prop('disabled', false);
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                        alert("<?= Yii::t('app', 'Error occurred while adding part') ?>");
                    }
                });
            }
        });
    }



    function initializePartPhoto() {
        $(document).off('click.part-photo');

        $(document).on('click.part-photo', '.part-photo', function() {
            var id = $(this).data('id');
            $.ajax({
                url: '/backend/equipment-part/get-photo',
                type: 'GET',
                data: {id: id},
                success: function(response) {
                    if (response.status === 'success') {
                        $('#ideal-large-modal-without-save .modal-title').text(response.name);
                        var content = '<div class="text-center"><img src="' + response.photo + '" alt="" style="max-width: 100%;"></div>';
                        $('#ideal-large-modal-without-save .modal-body').html(content);
                        $('#ideal-large-modal-without-save').modal('show');
                    } else {
                        alert(response.message);
                    }
                },
                error: function() {
                    alert("<?= Yii::t('app', 'Error loading photo') ?>");
                }
            });
        });

        // Очистка содержимого при закрытии
        $('#ideal-large-modal-without-save').on('hidden.bs.modal', function () {
            $('#ideal-large-modal-without-save .modal-body').html('');
            $('#ideal-large-modal-without-save .modal-title').text('');
        });
    }

    function initializePartDefect() {
        // Обработка брака для запчастей оборудования (новый метод)
        $(document).off('click.equipment-part-defect').on('click.equipment-part-defect', '.equipment-part-defect', function() {
            var equipmentId = $(this).data('equipment-id');
            var partId = $(this).data('part-id');

            $.ajax({
                url: '/backend/equipment/defect-part/' + equipmentId + '/' + partId,
                type: 'GET',
                success: function(response) {
                    $('#ideal-mini-modal .modal-title').html(three);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass('equipment-part-defect-save');
                    $('#ideal-mini-modal .mini-button').attr('data-equipment-id', equipmentId);
                    $('#ideal-mini-modal .mini-button').attr('data-part-id', partId);
                    initializeSelect2();
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                    console.error('Response:', xhr.responseText);
                    alert("<?= Yii::t('app', 'Error loading defect form') ?>");
                }
            });
        });

        // Обработка сохранения брака для запчастей оборудования
        $(document).off('click.equipment-part-defect-save').on('click.equipment-part-defect-save', '.equipment-part-defect-save', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                clearValidationErrors();

                var equipmentId = button.attr('data-equipment-id');
                var partId = button.attr('data-part-id');
                var formData = new FormData($('#part-defect-form')[0]);

                $.ajax({
                    url: '/backend/equipment/defect-part/' + equipmentId + '/' + partId,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({
                                container: '#parts-grid-pjax',
                                complete: function() {
                                    initializeDataTable();
                                }
                            });
                        } else {
                            button.prop('disabled', false);
                            if (response.errors) {
                                displayValidationErrors(response.errors);
                            } else {
                                alert(response.message);
                            }
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        button.prop('disabled', false);
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                        console.error('Response:', xhr.responseText);
                        alert("<?= Yii::t('app', 'Error occurred while processing defect') ?>");
                    }
                });
            }
        });

        // Старый обработчик для общих запчастей (оставляем для совместимости)
        $(document).off('click.part-defect').on('click.part-defect', '.part-defect', function() {
            var id = $(this).data('id');
            $.ajax({
                url: '/backend/equipment-part/defect',
                type: 'GET',
                data: {id: id},
                success: function(response) {
                    $('#ideal-mini-modal .modal-title').html(three);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass('part-defect-save');
                    $('#ideal-mini-modal .mini-button').attr('data-part-id', id);
                    initializeSelect2();
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.part-defect-save').on('click.part-defect-save', '.part-defect-save', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                clearValidationErrors();
                var id = button.attr('data-part-id');
                var formData = new FormData($('#part-defect-form')[0]);

                $.ajax({
                    url: '/backend/equipment-part/defect?id=' + id,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({
                                container: '#parts-grid-pjax',
                                complete: function() {
                                    initializeDataTable();
                                }
                            });
                        } else {
                            button.prop('disabled', false);
                            if (response.errors) {
                                displayValidationErrors(response.errors);
                            } else {
                                alert(response.message);
                            }
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        button.prop('disabled', false);
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                        alert("<?= Yii::t('app', 'Error occurred while processing defect') ?>");
                    }
                });
            }
        });
    }

    // Функция для очистки ошибок валидации
    function clearValidationErrors() {
        $('.form-control').removeClass('is-invalid');
        $('.invalid-feedback').text('').hide();
    }

    // Функция для отображения ошибок валидации
    function displayValidationErrors(errors) {
        $.each(errors, function(field, fieldErrors) {
            var input = $('[name="' + field + '"]');
            var errorDiv = $('#' + field + '-error');

            input.addClass('is-invalid');
            errorDiv.text(fieldErrors.join(', ')).show();
        });
    }

    function initializeDropdown() {
        $(document).off('click.dropdown').on('click.dropdown', '.dropdown-toggle', function(e) {
            e.preventDefault();
            e.stopPropagation();
            var dropdownMenu = $(this).siblings('.dropdown-menu');
            $('.dropdown-menu').not(dropdownMenu).removeClass('show');
            dropdownMenu.toggleClass('show');
        });

        $(document).off('click.dropdown-item').on('click.dropdown-item', '.dropdown-item', function(e) {
            // Разрешаем переход по ссылкам на историю
            if ($(this).attr('href') && $(this).attr('href').indexOf('/history') !== -1) {
                return true; // Позволяем стандартное поведение ссылки
            }
            e.preventDefault();
            e.stopPropagation();
        });

        $(document).off('click.dropdown-close').on('click.dropdown-close', function(e) {
            if (!$(e.target).closest('.dropdown').length) {
                $('.dropdown-menu').removeClass('show');
            }
        });
    }

    function initializeRowClick() {
        // Обработка кликов по строкам таблицы для перехода на историю
        $(document).off('click.row-click').on('click.row-click', '.clickable-row', function(e) {
            // Игнорируем клики по элементам управления (кнопки, ссылки, изображения)
            if ($(e.target).closest('.dropdown, .part-photo, a, button').length > 0) {
                return;
            }

            var href = $(this).data('href');
            if (href) {
                window.location.href = href;
            }
        });
    }

    function initializeAll() {
        initializeDataTable();
        initializeSelect2();
        initializeSearch();
        initializePartCreate();
        initializePartPhoto();
        initializePartDefect();
        initializePartStatusActions();
        initializeDropdown();
        initializeRowClick();
    }

    // Initialize everything on first load
    initializeAll();

    // Re-initialize after PJAX reloads
    $(document).on('pjax:success', function() {
        initializeAll();
    });

    // Инициализация обработчиков для резерва, ремонта и активации
    function initializePartStatusActions() {
        // Резерв
        $(document).off('click.equipment-part-reserve').on('click.equipment-part-reserve', '.equipment-part-reserve', function() {
            var equipmentId = $(this).data('equipment-id');
            var partId = $(this).data('part-id');

            $.ajax({
                url: '/backend/equipment/reserve-part/' + equipmentId + '/' + partId,
                type: 'GET',
                success: function(response) {
                    $('#ideal-mini-modal .modal-title').html(four);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass('equipment-part-reserve-save');
                    $('#ideal-mini-modal .mini-button').attr('data-equipment-id', equipmentId);
                    $('#ideal-mini-modal .mini-button').attr('data-part-id', partId);
                    initializeSelect2();
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                    alert("<?= Yii::t('app', 'Error loading reserve form') ?>");
                }
            });
        });

        // Ремонт
        $(document).off('click.equipment-part-repair').on('click.equipment-part-repair', '.equipment-part-repair', function() {
            var equipmentId = $(this).data('equipment-id');
            var partId = $(this).data('part-id');

            $.ajax({
                url: '/backend/equipment/repair-part/' + equipmentId + '/' + partId,
                type: 'GET',
                success: function(response) {
                    $('#ideal-mini-modal .modal-title').html(five);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass('equipment-part-repair-save');
                    $('#ideal-mini-modal .mini-button').attr('data-equipment-id', equipmentId);
                    $('#ideal-mini-modal .mini-button').attr('data-part-id', partId);
                    initializeSelect2();
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                    alert("<?= Yii::t('app', 'Error loading repair form') ?>");
                }
            });
        });

        // Активация
        $(document).off('click.equipment-part-activate').on('click.equipment-part-activate', '.equipment-part-activate', function() {
            var equipmentId = $(this).data('equipment-id');
            var partId = $(this).data('part-id');

            $.ajax({
                url: '/backend/equipment/activate-part/' + equipmentId + '/' + partId,
                type: 'GET',
                success: function(response) {
                    $('#ideal-mini-modal .modal-title').html(six);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass('equipment-part-activate-save');
                    $('#ideal-mini-modal .mini-button').attr('data-equipment-id', equipmentId);
                    $('#ideal-mini-modal .mini-button').attr('data-part-id', partId);
                    initializeSelect2();
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                    alert("<?= Yii::t('app', 'Error loading activate form') ?>");
                }
            });
        });

        // Сохранение резерва
        $(document).off('click.equipment-part-reserve-save').on('click.equipment-part-reserve-save', '.equipment-part-reserve-save', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                clearValidationErrors();

                var equipmentId = button.attr('data-equipment-id');
                var partId = button.attr('data-part-id');
                var formData = new FormData($('#part-reserve-form')[0]);

                $.ajax({
                    url: '/backend/equipment/reserve-part/' + equipmentId + '/' + partId,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({
                                container: '#parts-grid-pjax',
                                complete: function() {
                                    initializeDataTable();
                                }
                            });
                        } else {
                            button.prop('disabled', false);
                            if (response.errors) {
                                displayValidationErrors(response.errors);
                            } else {
                                alert(response.message);
                            }
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        button.prop('disabled', false);
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                        alert("<?= Yii::t('app', 'Error occurred while processing reserve') ?>");
                    }
                });
            }
        });

        // Сохранение ремонта
        $(document).off('click.equipment-part-repair-save').on('click.equipment-part-repair-save', '.equipment-part-repair-save', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                clearValidationErrors();

                var equipmentId = button.attr('data-equipment-id');
                var partId = button.attr('data-part-id');
                var formData = new FormData($('#part-repair-form')[0]);

                $.ajax({
                    url: '/backend/equipment/repair-part/' + equipmentId + '/' + partId,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({
                                container: '#parts-grid-pjax',
                                complete: function() {
                                    initializeDataTable();
                                }
                            });
                        } else {
                            button.prop('disabled', false);
                            if (response.errors) {
                                displayValidationErrors(response.errors);
                            } else {
                                alert(response.message);
                            }
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        button.prop('disabled', false);
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                        alert("<?= Yii::t('app', 'Error occurred while processing repair') ?>");
                    }
                });
            }
        });

        // Сохранение активации
        $(document).off('click.equipment-part-activate-save').on('click.equipment-part-activate-save', '.equipment-part-activate-save', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                clearValidationErrors();

                var equipmentId = button.attr('data-equipment-id');
                var partId = button.attr('data-part-id');
                var formData = new FormData($('#part-activate-form')[0]);

                $.ajax({
                    url: '/backend/equipment/activate-part/' + equipmentId + '/' + partId,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({
                                container: '#parts-grid-pjax',
                                complete: function() {
                                    initializeDataTable();
                                }
                            });
                        } else {
                            button.prop('disabled', false);
                            if (response.errors) {
                                displayValidationErrors(response.errors);
                            } else {
                                alert(response.message);
                            }
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        button.prop('disabled', false);
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                        alert("<?= Yii::t('app', 'Error occurred while processing activation') ?>");
                    }
                });
            }
        });
    }

    // Очистка обработчиков при закрытии модального окна
    $('#ideal-mini-modal').on('hidden.bs.modal', function () {
        $('.mini-button').removeClass('part-create-button part-update-button part-defect-save equipment-part-defect-save equipment-part-reserve-save equipment-part-repair-save equipment-part-activate-save');
        $('.mini-button').removeAttr('data-part-id data-equipment-id'); // Удаляем атрибуты с ID
        $('#ideal-mini-modal .modal-body').html('');
        $('#ideal-mini-modal .modal-title').text('');
        clearValidationErrors(); // Очищаем ошибки валидации
    });

})(jQuery);
JS;
$this->registerJs($js, View::POS_END);
?>