<?php
use yii\helpers\Html;
use app\assets\Select2Asset;

Select2Asset::register($this);
?>

<form id="part-attach-form">
    <input type="hidden" name="id" value="<?= $model->id ?>">
    
    <div class="form-group">
        <label class="form-label"><?= Yii::t('app', 'Equipment') ?></label>
        <select class="form-control select2" name="equipment_id" required>
            <option value=""><?= Yii::t('app', 'select_equipment') ?></option>
            <?php foreach ($equipments as $equipment): ?>
                <option value="<?= $equipment->id ?>"><?= Html::encode($equipment->name) ?></option>
            <?php endforeach; ?>
        </select>
    </div>
    
    <div class="form-group">
        <label class="form-label"><?= Yii::t('app', 'Installation Date') ?></label>
        <input type="date" class="form-control" name="installation_date" value="<?= date('Y-m-d') ?>" required>
    </div>
</form>
