<?php

use yii\helpers\Html;
use app\common\models\EquipmentPart;

?>

<form id="add-part-form" method="post">
    <div class="row">
        <div class="col-md-12">
            <div class="form-group">
                <label class="form-label"><?= Yii::t('app', 'select_part') ?></label>
                <select class="form-control select2" name="part_id" id="part_id" required>
                    <option value=""><?= Yii::t('app', 'select_part') ?></option>
                    <?php foreach ($availableParts as $part): ?>
                        <option value="<?= $part->id ?>" 
                                data-quantity="<?= $part->quantity ?>"
                                data-price="<?= $part->price ?>"
                                data-status="<?= $part->status ?>">
                            <?= Html::encode($part->name) ?> 
                            (<?= Yii::t('app', 'available') ?>: <?= $part->quantity ?> <?= Yii::t('app', 'pcs') ?>)
                            <?php if ($part->status == EquipmentPart::STATUS_RESERVE): ?>
                                - <?= Yii::t('app', 'reserve') ?>
                            <?php endif; ?>
                        </option>
                    <?php endforeach; ?>
                </select>
                <div class="invalid-feedback" id="part_id-error"></div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="form-group">
                <label class="form-label"><?= Yii::t('app', 'quantity') ?></label>
                <input type="number" 
                       class="form-control" 
                       name="quantity" 
                       id="quantity" 
                       min="1" 
                       max="1"
                       value="1" 
                       required>
                <div class="invalid-feedback" id="quantity-error"></div>
                <small class="form-text text-muted" id="quantity-help">
                    <?= Yii::t('app', 'max_available') ?>: <span id="max-quantity">0</span>
                </small>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label class="form-label"><?= Yii::t('app', 'installation_date') ?></label>
                <input type="date" 
                       class="form-control" 
                       name="installation_date" 
                       id="installation_date" 
                       value="<?= date('Y-m-d') ?>">
                <div class="invalid-feedback" id="installation_date-error"></div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="form-group">
                <label class="form-label"><?= Yii::t('app', 'comment') ?></label>
                <textarea class="form-control" 
                          name="comment" 
                          id="comment" 
                          rows="3" 
                          placeholder="<?= Yii::t('app', 'optional_comment') ?>"></textarea>
                <div class="invalid-feedback" id="comment-error"></div>
            </div>
        </div>
    </div>

</form>

<script>
$(document).ready(function() {
    // Обработчик изменения выбранной запчасти
    $('#part_id').on('change', function() {
        var selectedOption = $(this).find('option:selected');
        var quantity = selectedOption.data('quantity') || 0;
        var price = selectedOption.data('price') || 0;
        var status = selectedOption.data('status');
        
        if ($(this).val()) {
            // Обновляем максимальное количество
            $('#max-quantity').text(quantity);
            $('#quantity').attr('max', quantity);
            
            // Показываем информацию о запчасти
            var statusText = '';
            if (status == <?= EquipmentPart::STATUS_RESERVE ?>) {
                statusText = '<?= Yii::t('app', 'reserve') ?>';
            } else {
                statusText = '<?= Yii::t('app', 'available') ?>';
            }
            
            var priceText = price > 0 ? price + ' <?= Yii::t('app', 'currency') ?>' : '<?= Yii::t('app', 'not_specified') ?>';
            
            $('#part-details').html(
                '<div class="row">' +
                '<div class="col-md-4"><strong><?= Yii::t('app', 'status') ?>:</strong> ' + statusText + '</div>' +
                '<div class="col-md-4"><strong><?= Yii::t('app', 'available_quantity') ?>:</strong> ' + quantity + ' <?= Yii::t('app', 'pcs') ?></div>' +
                '<div class="col-md-4"><strong><?= Yii::t('app', 'price') ?>:</strong> ' + priceText + '</div>' +
                '</div>'
            );
            $('#part-info').show();
        } else {
            $('#max-quantity').text('0');
            $('#quantity').attr('max', '1');
            $('#part-info').hide();
        }
    });
    
    // Валидация количества
    $('#quantity').on('input', function() {
        var max = parseInt($(this).attr('max'));
        var value = parseInt($(this).val());
        
        if (value > max) {
            $(this).val(max);
        }
        if (value < 1) {
            $(this).val(1);
        }
    });
});
</script>
