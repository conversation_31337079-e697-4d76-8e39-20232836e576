<?php
use yii\helpers\Html;
use app\assets\Select2Asset;
use app\common\models\EquipmentPart;

Select2Asset::register($this);
?>

<form id="part-status-form">
    <div class="form-group">
        <label class="form-label"><?= Yii::t('app', 'select_status') ?></label>
        <select class="form-control select2" name="status" required>
            <?php foreach (EquipmentPart::getStatusLabels() as $value => $label): ?>
                <?php if ($value != $model->status): ?>
                    <option value="<?= $value ?>"><?= $label ?></option>
                <?php endif; ?>
            <?php endforeach; ?>
        </select>
    </div>
    
    <div class="form-group">
        <label class="form-label"><?= Yii::t('app', 'comment') ?></label>
        <textarea class="form-control" name="comment" rows="3"></textarea>
    </div>
</form>
