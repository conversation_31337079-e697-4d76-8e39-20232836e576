<?php
use yii\helpers\Html;
use yii\helpers\Url;
use app\common\models\EquipmentPart;
?>

<table id="parts-grid-view" class="table table-bordered table-striped compact">
    <thead>
        <th><?= Yii::t("app", "photo") ?></th>
        <th><?= Yii::t("app", "name") ?></th>
        <th><?= Yii::t("app", "quantity") ?></th>
        <th><?= Yii::t("app", "installation_date") ?></th>
        <th><?= Yii::t("app", "price") ?></th>
        <th><?= Yii::t("app", "status") ?></th>
        <th><?= Yii::t("app", "comment") ?></th>
        <th><?= Yii::t("app", "actions") ?></th>
    </thead>
    <tbody>
    <?php if (!empty($result)): ?>
        <?php foreach ($result as $part): ?>
            <tr>
                <td>
                    <?php if ($part['photo']): ?>
                        <img src="/uploads/equipment_parts/<?= Html::encode($part['photo']) ?>"
                             alt="Part"
                             style="max-width: 50px; cursor: pointer;"
                             class="part-photo"
                             data-id="<?= $part['id'] ?>">
                    <?php endif; ?>
                </td>
                <td><?= Html::encode($part['name']) ?></td>
                <td>
                    <span class="badge badge-<?= $part['quantity'] > 0 ? 'success' : 'danger' ?>">
                        <?= $part['quantity'] ?? 0 ?>
                    </span>
                </td>
                <td data-order="<?= $part['installation_date'] ? strtotime($part['installation_date']) : 0 ?>">
                    <?php if ($part['installation_date']): ?>
                        <?= date('d.m.Y', strtotime($part['installation_date'])) ?>
                    <?php else: ?>
                        <span class="text-muted"><?= Yii::t('app', 'not_installed') ?></span>
                    <?php endif; ?>
                </td>
                <td><?= $part['price'] ? Yii::$app->formatter->asDecimal($part['price'], 2) : '0.00' ?></td>
                <td>
                    <span class="badge badge-<?= $part['status'] == EquipmentPart::STATUS_INACTIVE ? 'danger' : ($part['status'] == EquipmentPart::STATUS_ACTIVE ? 'success' : 'warning') ?>">
                        <?= $part['status'] == EquipmentPart::STATUS_INACTIVE ? Yii::t("app", "inactive") : ($part['status'] == EquipmentPart::STATUS_ACTIVE ? Yii::t("app", "active") : Yii::t("app", "reserve")) ?>
                    </span>
                </td>
                <td>
                    <?php if ($part['comment']): ?>
                        <?= Html::encode(mb_strlen($part['comment']) > 50 ? mb_substr($part['comment'], 0, 50) . '...' : $part['comment']) ?>
                    <?php else: ?>
                        <span class="text-muted">-</span>
                    <?php endif; ?>
                </td>
                <td>
                    <div class="dropdown d-inline">
                        <a href="#" class="badge badge-info dropdown-toggle" data-toggle="dropdown">
                            <?php echo Yii::t("app", "detail"); ?>
                        </a>
                        <div class="dropdown-menu">
                            <?php if ($part['deleted_at'] == NULL): ?>
                                <a href="#" class="dropdown-item part-update" data-toggle="modal" data-target="#ideal-mini-modal" data-id="<?= Html::encode($part['id']) ?>">
                                    <?= Yii::t("app", "edit") ?>
                                </a>

                                <a href="<?= Url::to(['/backend/equipment-part/history', 'id' => $part['id']]) ?>" class="dropdown-item">
                                    <?= Yii::t("app", "history") ?>
                                </a>

                                <a href="#" class="dropdown-item part-defect" data-toggle="modal" data-target="#ideal-mini-modal" data-id="<?= Html::encode($part['id']) ?>">
                                    <?= Yii::t("app", "defect") ?>
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </td>
            </tr>
        <?php endforeach; ?>
    <?php endif; ?>
    </tbody>
</table>
