<?php

use yii\db\Migration;

/**
 * Class m250126_052000_add_status_quantities_to_equipment_part_assignments
 */
class m250126_052000_add_status_quantities_to_equipment_part_assignments extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        // Добавляем поля для отслеживания количества в разных статусах
        $this->addColumn('equipment_part_assignments', 'active_quantity', $this->integer()->defaultValue(0)->comment('Количество в активном состоянии'));
        $this->addColumn('equipment_part_assignments', 'repair_quantity', $this->integer()->defaultValue(0)->comment('Количество в ремонте'));
        
    
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropColumn('equipment_part_assignments', 'active_quantity');
        $this->dropColumn('equipment_part_assignments', 'repair_quantity');
    }
}
