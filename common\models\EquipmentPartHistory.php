<?php

namespace app\common\models;

use Yii;
use yii\db\ActiveRecord;
use app\modules\backend\models\Users;

/**
 * This is the model class for table "equipment_part_history".
 *
 * @property int $id
 * @property int $equipment_part_id
 * @property int|null $equipment_id
 * @property int $action_type
 * @property int|null $status_before
 * @property int|null $status_after
 * @property int|null $quantity
 * @property string|null $comment
 * @property string $created_at
 * @property int|null $created_by
 *
 * @property Equipment $equipment
 * @property EquipmentPart $equipmentPart
 * @property Users $createdBy
 */
class EquipmentPartHistory extends ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'equipment_part_history';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['equipment_part_id', 'action_type'], 'required'],
            [['equipment_part_id', 'equipment_id', 'action_type', 'status_before', 'status_after', 'quantity', 'created_by'], 'integer'],
            [['quantity'], 'integer', 'min' => 1],
            [['comment'], 'string'],
            [['created_at'], 'safe'],
            [['equipment_part_id'], 'exist', 'skipOnError' => true, 'targetClass' => EquipmentPart::class, 'targetAttribute' => ['equipment_part_id' => 'id']],
            [['equipment_id'], 'exist', 'skipOnError' => true, 'targetClass' => Equipment::class, 'targetAttribute' => ['equipment_id' => 'id']],
            [['created_by'], 'exist', 'skipOnError' => true, 'targetClass' => Users::class, 'targetAttribute' => ['created_by' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => Yii::t('app', 'ID'),
            'equipment_part_id' => Yii::t('app', 'Equipment Part'),
            'equipment_id' => Yii::t('app', 'Equipment'),
            'action_type' => Yii::t('app', 'Action Type'),
            'status_before' => Yii::t('app', 'Status Before'),
            'status_after' => Yii::t('app', 'Status After'),
            'quantity' => Yii::t('app', 'Quantity'),
            'comment' => Yii::t('app', 'Comment'),
            'created_at' => Yii::t('app', 'Created At'),
            'created_by' => Yii::t('app', 'Created By'),
        ];
    }

    /**
     * Gets query for [[Equipment]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getEquipment()
    {
        return $this->hasOne(Equipment::class, ['id' => 'equipment_id']);
    }

    /**
     * Gets query for [[EquipmentPart]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getEquipmentPart()
    {
        return $this->hasOne(EquipmentPart::class, ['id' => 'equipment_part_id']);
    }

    /**
     * Gets query for [[CreatedBy]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getCreatedBy()
    {
        return $this->hasOne(Users::class, ['id' => 'created_by']);
    }
}
