<?php

namespace app\common\models;

use Yii;
use yii\db\ActiveRecord;
use yii\behaviors\TimestampBehavior;
use yii\db\Expression;

/**
 * This is the model class for table "equipment_parts".
 *
 * @property int $id
 * @property int|null $equipment_id
 * @property string $name
 * @property string $photo
 * @property string|null $description
 * @property float|null $price
 * @property int $source_type
 * @property int $status
 * @property string|null $installation_date
 * @property int $quantity
 * @property string|null $last_purchase_date
 * @property string|null $comment
 * @property string|null $created_at
 * @property string|null $deleted_at
 *
 * @property Equipment $equipment
 * @property EquipmentPartHistory[] $history
 * @property EquipmentPartMovement[] $movements
 */
class EquipmentPart extends ActiveRecord
{
    const SCENARIO_CREATE = 'create';
    const SCENARIO_UPDATE = 'update';
    const SCENARIO_CHANGE_STATUS = 'change_status';
    const SCENARIO_ATTACH = 'attach';
    const SCENARIO_DETACH = 'detach';

    // Статусы запчастей
    const STATUS_ACTIVE = 1;    // Активная запчасть
    const STATUS_INACTIVE = 0;  // Выведена из эксплуатации - "ишдан чиқди"
    const STATUS_RESERVE = 2;   // В резерве - "захирага олинди"

    // Типы источников
    const SOURCE_PURCHASED = 1; // "Сотиб олинган" - приобретенные извне
    const SOURCE_RESERVE = 2;   // "Захира" - выданные со склада завода

    // Типы действий в истории
    const ACTION_INSTALL = 1;   // Установка
    const ACTION_REMOVE = 2;    // Снятие
    const ACTION_RESERVE = 3;   // Перемещение в резерв
    const ACTION_CREATE = 4;    // Создание запчасти
    const ACTION_REPAIR = 5;    // Отправка в ремонт
    const ACTION_ACTIVATE = 6;  // Возврат из ремонта

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'equipment_parts';
    }

    /**
     * {@inheritdoc}
     */
    public function scenarios()
    {
        $scenarios = parent::scenarios();
        $scenarios[self::SCENARIO_CREATE] = ['name', 'photo', 'price', 'source_type', 'status', 'equipment_id', 'installation_date', 'quantity', 'comment'];
        $scenarios[self::SCENARIO_UPDATE] = ['name', 'photo', 'price', 'source_type', 'status', 'equipment_id', 'installation_date', 'quantity', 'comment'];
        $scenarios[self::SCENARIO_CHANGE_STATUS] = ['status'];
        $scenarios[self::SCENARIO_ATTACH] = ['equipment_id', 'installation_date'];
        $scenarios[self::SCENARIO_DETACH] = ['equipment_id'];
        return $scenarios;
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['name', 'photo', 'source_type', 'status'], 'required'],
            [['equipment_id', 'quantity'], 'integer'],
            [['comment'], 'string'],
            [['price'], 'number'],
            [['installation_date', 'last_purchase_date', 'created_at', 'deleted_at'], 'safe'],
            [['name', 'photo'], 'string', 'max' => 255],
            [['source_type', 'status'], 'integer'],
            [['quantity'], 'integer', 'min' => 0],
            [['quantity'], 'default', 'value' => 0],
            [['equipment_id'], 'exist', 'skipOnError' => true, 'targetClass' => Equipment::class, 'targetAttribute' => ['equipment_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => Yii::t('app', 'ID'),
            'equipment_id' => Yii::t('app', 'Equipment'),
            'name' => Yii::t('app', 'Name'),
            'photo' => Yii::t('app', 'Photo'),
            'description' => Yii::t('app', 'Description'),
            'price' => Yii::t('app', 'Price'),
            'source_type' => Yii::t('app', 'Source Type'),
            'status' => Yii::t('app', 'Status'),
            'installation_date' => Yii::t('app', 'Installation Date'),
            'quantity' => Yii::t('app', 'Quantity'),
            'last_purchase_date' => Yii::t('app', 'Last Purchase Date'),
            'comment' => Yii::t('app', 'Comment'),
            'created_at' => Yii::t('app', 'Created At'),
            'deleted_at' => Yii::t('app', 'Deleted At'),
        ];
    }

    /**
     * Получить текстовые метки для статусов
     * @return array
     */
    public static function getStatusLabels()
    {
        return [
            self::STATUS_ACTIVE => Yii::t('app', 'active'),
            self::STATUS_INACTIVE => Yii::t('app', 'inactive'),
            self::STATUS_RESERVE => Yii::t('app', 'reserve'),
        ];
    }

    /**
     * Получить текстовую метку для статуса
     * @param int $status
     * @return string
     */
    public static function getStatusLabel($status)
    {
        return self::getStatusLabels()[$status] ?? 'Noma`lum';
    }

    /**
     * Получить текстовые метки для типов источников
     * @return array
     */
    public static function getSourceTypeLabels()
    {
        return [
            self::SOURCE_PURCHASED => Yii::t('app', 'purchased'),
            self::SOURCE_RESERVE => Yii::t('app', 'reserve'),
        ];
    }

    /**
     * Получить текстовую метку для типа источника
     * @param int $sourceType
     * @return string
     */
    public static function getSourceTypeLabel($sourceType)
    {
        return self::getSourceTypeLabels()[$sourceType] ?? 'Noma`lum';
    }

    /**
     * Получить текстовые метки для типов действий
     * @return array
     */
    public static function getActionTypeLabels()
    {
        return [
            self::ACTION_INSTALL => Yii::t('app', 'installed'),
            self::ACTION_REMOVE => Yii::t('app', 'removed'),
            self::ACTION_RESERVE => Yii::t('app', 'moved_to_reserve'),
            self::ACTION_CREATE => Yii::t('app', 'created'),
            self::ACTION_REPAIR => Yii::t('app', 'sent_to_repair'),
            self::ACTION_ACTIVATE => Yii::t('app', 'returned_from_repair'),
        ];
    }

    /**
     * Получить текстовую метку для типа действия
     * @param int $actionType
     * @return string
     */
    public static function getActionTypeLabel($actionType)
    {
        return self::getActionTypeLabels()[$actionType] ?? 'Noma`lum';
    }

    /**
     * Gets query for [[Equipment]] (deprecated - use assignments instead).
     * @deprecated Use getAssignments() instead
     * @return \yii\db\ActiveQuery
     */
    public function getEquipment()
    {
        return $this->hasOne(Equipment::class, ['id' => 'equipment_id']);
    }

    /**
     * Gets query for [[Assignments]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getAssignments()
    {
        return $this->hasMany(EquipmentPartAssignment::class, ['equipment_part_id' => 'id']);
    }

    /**
     * Gets query for active assignments.
     *
     * @return \yii\db\ActiveQuery
     */
    public function getActiveAssignments()
    {
        return $this->hasMany(EquipmentPartAssignment::class, ['equipment_part_id' => 'id'])
            ->where(['status' => EquipmentPartAssignment::STATUS_ACTIVE]);
    }

    /**
     * Gets query for [[EquipmentPartHistory]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getHistory()
    {
        return $this->hasMany(EquipmentPartHistory::class, ['equipment_part_id' => 'id']);
    }

    /**
     * Gets query for [[EquipmentPartMovement]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getMovements()
    {
        return $this->hasMany(EquipmentPartMovement::class, ['equipment_part_id' => 'id']);
    }

    /**
     * Добавить запись в историю
     *
     * @param int $actionType Тип действия
     * @param int|null $statusBefore Статус до изменения
     * @param int|null $statusAfter Статус после изменения
     * @param string|null $comment Комментарий
     * @param int|null $equipmentId ID оборудования
     * @param int|null $quantity Количество единиц для операции
     * @return bool
     */
    public function addHistory($actionType, $statusBefore = null, $statusAfter = null, $comment = null, $equipmentId = null, $quantity = null)
    {
        $history = new EquipmentPartHistory();
        $history->equipment_part_id = $this->id;
        $history->equipment_id = $equipmentId ?? $this->equipment_id;
        $history->action_type = $actionType;
        $history->status_before = $statusBefore;
        $history->status_after = $statusAfter;
        $history->quantity = $quantity;
        $history->comment = $comment;
        $history->created_by = Yii::$app->user->id;

        return $history->save();
    }

    /**
     * Get total assigned quantity
     * @return int
     */
    public function getAssignedQuantity()
    {
        return EquipmentPartAssignment::getTotalAssignedQuantity($this->id);
    }

    /**
     * Get available quantity (total - assigned)
     * @return int
     */
    public function getAvailableQuantity()
    {
        return max(0, $this->quantity - $this->getAssignedQuantity());
    }

    /**
     * Check if part can be assigned to equipment
     * @param int $requestedQuantity
     * @return bool
     */
    public function canAssign($requestedQuantity)
    {
        return $this->getAvailableQuantity() >= $requestedQuantity;
    }

    /**
     * Get equipment where this part is currently installed
     * @return Equipment[]
     */
    public function getInstalledEquipment()
    {
        return Equipment::find()
            ->joinWith('partAssignments')
            ->where(['equipment_part_assignments.equipment_part_id' => $this->id])
            ->andWhere(['equipment_part_assignments.status' => EquipmentPartAssignment::STATUS_ACTIVE])
            ->all();
    }
}
