<?php

use yii\db\Migration;

/**
 * Updates equipment_parts table structure and creates parts movement tracking
 */
class m250102_000001_update_equipment_parts_structure extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        // Добавляем новые поля в таблицу equipment_parts
        $this->addColumn('equipment_parts', 'quantity', $this->integer()->defaultValue(0)->notNull());
        $this->addColumn('equipment_parts', 'last_purchase_date', $this->date()->null());
        $this->addColumn('equipment_parts', 'comment', $this->text());

        // Создаем таблицу для учета движения запчастей
        $this->createTable('equipment_part_movements', [
            'id' => $this->bigPrimaryKey(),
            'equipment_part_id' => $this->bigInteger()->notNull(),
            'movement_type' => $this->integer()->notNull(), // 1-приход, 2-расход, 3-брак
            'quantity' => $this->integer()->notNull(),
            'price_per_unit' => $this->decimal(10, 2)->null(),
            'total_amount' => $this->decimal(10, 2)->null(),
            'comment' => $this->text(),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'created_by' => $this->bigInteger()->null(),
        ]);

        // Создаем внешние ключи для таблицы движений
        $this->addForeignKey(
            'fk-equipment_part_movements-equipment_part_id',
            'equipment_part_movements',
            'equipment_part_id',
            'equipment_parts',
            'id',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk-equipment_part_movements-created_by',
            'equipment_part_movements',
            'created_by',
            'users',
            'id',
            'SET NULL'
        );

        // Создаем индексы для оптимизации запросов
        $this->createIndex(
            'idx-equipment_part_movements-equipment_part_id',
            'equipment_part_movements',
            'equipment_part_id'
        );

        $this->createIndex(
            'idx-equipment_part_movements-movement_type',
            'equipment_part_movements',
            'movement_type'
        );

        $this->createIndex(
            'idx-equipment_part_movements-created_at',
            'equipment_part_movements',
            'created_at'
        );

        // Обновляем существующие записи - устанавливаем количество = 0 для всех запчастей
        $this->update('equipment_parts', ['quantity' => 0], '1=1');
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        // Удаляем внешние ключи и индексы
        $this->dropForeignKey('fk-equipment_part_movements-created_by', 'equipment_part_movements');
        $this->dropForeignKey('fk-equipment_part_movements-equipment_part_id', 'equipment_part_movements');
        
        $this->dropIndex('idx-equipment_part_movements-created_at', 'equipment_part_movements');
        $this->dropIndex('idx-equipment_part_movements-movement_type', 'equipment_part_movements');
        $this->dropIndex('idx-equipment_part_movements-equipment_part_id', 'equipment_part_movements');

        // Удаляем таблицу движений
        $this->dropTable('equipment_part_movements');

        // Удаляем добавленные колонки
        $this->dropColumn('equipment_parts', 'comment');
        $this->dropColumn('equipment_parts', 'last_purchase_date');
        $this->dropColumn('equipment_parts', 'quantity');
    }
}
