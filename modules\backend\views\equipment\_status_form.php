<?php
use yii\helpers\Html;
use app\assets\Select2Asset;
use app\common\models\Equipment;

Select2Asset::register($this);
?>

<form id="equipment-status-form">
    <div class="form-group">
        <label class="form-label"><?= Yii::t('app', 'select_status') ?></label>
        <select class="form-control select2" name="status" id="equipment-status" required>
            <?php if ($model->status != Equipment::STATUS_ACTIVE): ?>
                <option value="<?= Equipment::STATUS_ACTIVE ?>"><?= Yii::t('app', 'return_to_work') ?></option>
            <?php endif; ?>
            <?php if ($model->status != Equipment::STATUS_REPAIR): ?>
                <option value="<?= Equipment::STATUS_REPAIR ?>"><?= Yii::t('app', 'send_to_repair') ?></option>
            <?php endif; ?>
            <?php if ($model->status != Equipment::STATUS_INACTIVE): ?>
                <option value="<?= Equipment::STATUS_INACTIVE ?>"><?= Yii::t('app', 'write_off') ?></option>
            <?php endif; ?>
        </select>
        <div class="invalid-feedback" id="status-error"></div>
    </div>

    <!-- Блок для выбора запчастей при списании оборудования -->
    <div id="parts-selection-block" style="display: none; margin-top: 1rem;">
        <div class="alert alert-info">
            <ul class="mb-0">
                <li><?= Yii::t('app', 'selected_parts_returned_to_warehouse') ?></li>
                <li><?= Yii::t('app', 'unselected_parts_sent_to_defect') ?></li>
            </ul>
        </div>

        <?php if (!empty($parts)): ?>
            <div class="form-group">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <label class="form-label mb-0"><?= Yii::t('app', 'select_parts_to_return_to_warehouse') ?></label>
                    <div>
                        <button type="button" class="btn btn-sm btn-outline-primary" id="select-all-parts">
                            <?= Yii::t('app', 'select_all') ?>
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary ml-1" id="deselect-all-parts" style="margin-top: 1px;">
                            <?= Yii::t('app', 'deselect_all') ?>
                        </button>
                    </div>
                </div>

                <div class="parts-list border rounded p-3" style="max-height: 300px; overflow-y: auto;">
                    <?php foreach ($parts as $part): ?>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" name="parts[]" value="<?= $part['id'] ?>" id="part-<?= $part['id'] ?>">
                            <label class="form-check-label d-flex align-items-center" for="part-<?= $part['id'] ?>">
                                <div class="mr-3">
                                    <?php if (!empty($part['photo'])): ?>
                                        <img src="/uploads/equipment_parts/<?= Html::encode($part['photo']) ?>"
                                             alt="<?= Html::encode($part['name']) ?>"
                                             class="part-thumbnail">
                                    <?php else: ?>
                                        <div class="part-thumbnail-placeholder">
                                            <i class="fas fa-image"></i>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="font-weight-bold"><?= Html::encode($part['name']) ?></div>
                                    <div class="text-muted small">
                                        <?= Yii::t('app', 'quantity') ?>:
                                        <?php
                                        $totalQuantity = ($part['active_quantity'] ?? 0) + ($part['repair_quantity'] ?? 0);
                                        echo number_format($totalQuantity, 0, ',', ' ');
                                        ?>
                                        <?php if (($part['active_quantity'] ?? 0) > 0 && ($part['repair_quantity'] ?? 0) > 0): ?>
                                            <span class="text-info">
                                                (<?= Yii::t('app', 'active') ?>: <?= number_format($part['active_quantity'], 0, ',', ' ') ?>,
                                                <?= Yii::t('app', 'repair') ?>: <?= number_format($part['repair_quantity'], 0, ',', ' ') ?>)
                                            </span>
                                        <?php elseif (($part['repair_quantity'] ?? 0) > 0): ?>
                                            <span class="text-warning">(<?= Yii::t('app', 'in_repair') ?>)</span>
                                        <?php endif; ?>
                                    </div>
                                    <?php if (!empty($part['assignment_comment'])): ?>
                                        <div class="text-muted small">
                                            <i class="fas fa-comment mr-1"></i><?= Html::encode($part['assignment_comment']) ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </label>
                        </div>
                    <?php endforeach; ?>
                </div>

                <small class="form-text text-muted mt-2">
                    <i class="fas fa-exclamation-triangle text-warning mr-1"></i>
                    <?= Yii::t('app', 'parts_decommission_warning') ?>
                </small>
            </div>
        <?php else: ?>
            <div class="alert alert-secondary">
                <i class="fas fa-info-circle mr-2"></i>
                <?= Yii::t('app', 'no_parts_attached_to_equipment') ?>
            </div>
        <?php endif; ?>
    </div>
</form>

<style>
.invalid-feedback {
    display: none;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #dc3545;
}

.is-invalid ~ .invalid-feedback {
    display: block;
}

.form-control.is-invalid {
    border-color: #dc3545;
}

.part-thumbnail {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 4px;
    border: 1px solid #dee2e6;
}

.part-thumbnail-placeholder {
    width: 40px;
    height: 40px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
}

.parts-list {
    background-color: #f8f9fa;
}

.form-check {
    padding: 0.5rem;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.form-check:hover {
    background-color: rgba(0, 123, 255, 0.1);
}

.form-check-input:checked ~ .form-check-label {
    color: #007bff;
}

.alert ul {
    padding-left: 1.2rem;
}

.alert li {
    margin-bottom: 0.25rem;
}
</style>

<script>
    $(document).ready(function() {
        // Показываем/скрываем блок выбора запчастей в зависимости от выбранного статуса
        $('#equipment-status').on('change', function() {
            if ($(this).val() == '<?= Equipment::STATUS_INACTIVE ?>') {
                $('#parts-selection-block').slideDown();
            } else {
                $('#parts-selection-block').slideUp();
            }
        });

        // Кнопка "Выбрать все"
        $('#select-all-parts').on('click', function() {
            $('#parts-selection-block input[type="checkbox"]').prop('checked', true);
        });

        // Кнопка "Снять выделение"
        $('#deselect-all-parts').on('click', function() {
            $('#parts-selection-block input[type="checkbox"]').prop('checked', false);
        });

        // Инициализация Select2 для статуса
        $('#equipment-status').select2({
            width: '100%',
            placeholder: '<?= Yii::t('app', 'select_status') ?>',
            allowClear: false
        });
    });
</script>
