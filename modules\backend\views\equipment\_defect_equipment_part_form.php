<?php
use yii\helpers\Html;
use app\assets\Select2Asset;

Select2Asset::register($this);
?>

<form id="part-defect-form">
    <input type="hidden" name="equipment_id" value="<?= $equipment->id ?>">
    <input type="hidden" name="part_id" value="<?= $part->id ?>">

    <div class="form-group">
        <label class="form-label"><?= Yii::t('app', 'part_name') ?></label>
        <p><strong><?= Html::encode($part->name) ?></strong></p>
    </div>

    <div class="form-group">
        <label class="form-label"><?= Yii::t('app', 'assigned_quantity') ?></label>
        <p><strong><?= $assignment->quantity ?></strong> <?= Yii::t('app', 'units_assigned_to_equipment') ?></p>
    </div>

    <div class="form-group">
        <label class="form-label"><?= Yii::t('app', 'defect_quantity') ?></label>
        <input type="number" class="form-control" name="quantity" min="1" max="<?= $assignment->quantity ?>" required>
        <small class="form-text text-muted">
            <?= Yii::t('app', 'max_available_for_defect', ['max' => $assignment->quantity]) ?>
        </small>
        <div class="invalid-feedback" id="quantity-error"></div>
    </div>

    <div class="form-group">
        <label class="form-label"><?= Yii::t('app', 'defect_reason') ?></label>
        <textarea class="form-control" name="comment" rows="3" required></textarea>
        <div class="invalid-feedback" id="comment-error"></div>
    </div>
</form>
