<?php

use yii\helpers\Html;
use yii\helpers\Url;
use app\common\models\EquipmentPart;
use app\common\models\EquipmentPartHistory;
use app\common\models\EquipmentPartMovement;
use app\common\models\Equipment;
use app\modules\backend\models\Users;

$this->title = Yii::t('app', 'part_history') . ': ' . $model->name;
$this->params['breadcrumbs'][] = ['label' => Yii::t('app', 'Equipment Parts'), 'url' => ['index']];
$this->params['breadcrumbs'][] = ['label' => $model->name, 'url' => ['view', 'id' => $model->id]];
$this->params['breadcrumbs'][] = Yii::t('app', 'part_history');
$referar = Yii::$app->request->referrer;

// Получаем параметры фильтрации из запроса
$startDate = Yii::$app->request->get('start_date', date('d.m.Y', strtotime('-1 month')));
$endDate = Yii::$app->request->get('end_date', date('d.m.Y'));
?>

<div class="equipment-part-history">
    <div class="card-body">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <div>
                <h4 class="my-0"><?= Html::encode($this->title) ?></h4>
            </div>
            <div class="d-flex align-items-center">
                <form id="date-filter-form" class="d-flex align-items-center" method="get" action="<?= Url::to(['history', 'id' => $model->id]) ?>">
                    <div class="d-flex align-items-center">
                        <input type="date" name="start_date" class="form-control form-control-sm" value="<?= date('Y-m-d', strtotime($startDate)) ?>" style="width: 150px;" />
                        <span class="mx-2"></span>
                        <input type="date" name="end_date" class="form-control form-control-sm" value="<?= date('Y-m-d', strtotime($endDate)) ?>" style="width: 150px;" />
                        <button type="submit" class="btn btn-primary btn-sm ms-2">
                            <?= Yii::t('app', 'search') ?>
                        </button>
                    </div>
                </form>

                <a href="<?= $referar ?>" class="btn btn-primary btn-sm ms-3">
                    <?= Yii::t('app', 'back_to_part') ?>
                </a>
            </div>
        </div>


            <?php if (empty($historyData) && empty($movements)): ?>
                <div class="alert alert-info text-center" style="color: #4a5b6d;">
                    <?= Yii::t('app', 'no_history_records_found') ?>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th><?= Yii::t('app', 'date') ?></th>
                                <th><?= Yii::t('app', 'type') ?></th>
                                <th><?= Yii::t('app', 'quantity') ?></th>
                                <th><?= Yii::t('app', 'price_per_unit') ?></th>
                                <th><?= Yii::t('app', 'total_amount') ?></th>
                                <th><?= Yii::t('app', 'comment') ?></th>
                                <th><?= Yii::t('app', 'user') ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            // Объединяем историю действий и движения в один массив для сортировки
                            $allRecords = [];

                            // Добавляем историю действий
                            foreach ($historyData as $record) {
                                $allRecords[] = [
                                    'type' => 'history',
                                    'data' => $record,
                                    'created_at' => $record['created_at']
                                ];
                            }

                            // Добавляем движения
                            foreach ($movements as $movement) {
                                $allRecords[] = [
                                    'type' => 'movement',
                                    'data' => $movement,
                                    'created_at' => $movement->created_at
                                ];
                            }

                            // Сортируем по дате (новые сверху)
                            usort($allRecords, function($a, $b) {
                                return strtotime($b['created_at']) - strtotime($a['created_at']);
                            });
                            ?>

                            <?php foreach ($allRecords as $record): ?>
                                <?php if ($record['type'] == 'history'): ?>
                                    <?php $historyRecord = $record['data']; ?>
                                    <tr class="table-info">
                                        <td><?= date('d.m.Y H:i', strtotime($historyRecord['created_at'])) ?></td>
                                        <td>
                                            <span class="badge badge-info">
                                                <?php
                                                switch ($historyRecord['action_type']) {
                                                    case EquipmentPart::ACTION_INSTALL:
                                                        echo Yii::t('app', 'installation');
                                                        break;
                                                    case EquipmentPart::ACTION_REMOVE:
                                                        echo Yii::t('app', 'removal');
                                                        break;
                                                    case EquipmentPart::ACTION_CREATE:
                                                        echo Yii::t('app', 'creation');
                                                        break;
                                                    case EquipmentPart::ACTION_RESERVE:
                                                        echo Yii::t('app', 'reserve');
                                                        break;
                                                    case EquipmentPart::ACTION_REPAIR:
                                                        echo Yii::t('app', 'sent_to_repair');
                                                        break;
                                                    case EquipmentPart::ACTION_ACTIVATE:
                                                        echo Yii::t('app', 'returned_from_repair');
                                                        break;
                                                    default:
                                                        echo Yii::t('app', 'action');
                                                }
                                                ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php
                                            // Показываем количество из истории (приоритет) или из назначения
                                            $quantity = $historyRecord['assignment_quantity'] ?? $historyRecord['quantity'] ?? null;

                                            if (in_array($historyRecord['action_type'], [EquipmentPart::ACTION_INSTALL, EquipmentPart::ACTION_RESERVE, EquipmentPart::ACTION_REPAIR, EquipmentPart::ACTION_ACTIVATE]) && !empty($quantity)): ?>
                                                <span class="text-primary"><?= number_format($quantity, 0, ',', ' ') ?></span>
                                            <?php elseif ($historyRecord['action_type'] == EquipmentPart::ACTION_REMOVE): ?>
                                                <span class="text-warning">снято</span>
                                            <?php else: ?>
                                                -
                                            <?php endif; ?>
                                        </td>
                                        <td>-</td>
                                        <td>-</td>
                                        <td>
                                            <?= Html::encode($historyRecord['comment']) ?>
                                            <?php if (!empty($historyRecord['equipment_id'])): ?>
                                                <?php
                                                // Получаем название оборудования
                                                $equipment = Equipment::findOne($historyRecord['equipment_id']);
                                                if ($equipment): ?>
                                                    <br><small class="text-muted"><?= Yii::t('app', 'equipment') ?>: <?= Html::encode($equipment->name) ?></small>
                                                <?php endif; ?>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php
                                            // Получаем пользователя
                                            $user = Users::findOne($historyRecord['created_by']);
                                            echo $user ? Html::encode($user->username) : '-';
                                            ?>
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php $movement = $record['data']; ?>
                                <tr class="<?= $movement->movement_type == EquipmentPartMovement::MOVEMENT_DEFECT ? 'table-danger' : ($movement->movement_type == EquipmentPartMovement::MOVEMENT_INCOME ? 'table-success' : 'table-warning') ?>">
                                    <td><?= date('d.m.Y H:i:s', strtotime($movement->created_at)) ?></td>
                                    <td>
                                        <span class="badge badge-<?= $movement->movement_type == EquipmentPartMovement::MOVEMENT_DEFECT ? 'danger' : ($movement->movement_type == EquipmentPartMovement::MOVEMENT_INCOME ? 'success' : 'warning') ?>">
                                            <?= EquipmentPartMovement::getMovementTypeLabel($movement->movement_type) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($movement->movement_type == EquipmentPartMovement::MOVEMENT_INCOME): ?>
                                            <span class="text-prime">+<?= $movement->quantity ?></span>
                                        <?php elseif ($movement->movement_type == EquipmentPartMovement::MOVEMENT_OUTCOME): ?>
                                            <span class="text-warning">-<?= $movement->quantity ?></span>
                                        <?php else: ?>
                                            <span class="text-danger">-<?= $movement->quantity ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?= $movement->price_per_unit ? Yii::$app->formatter->asDecimal($movement->price_per_unit, 2) : '-' ?>
                                    </td>
                                    <td>
                                        <?= $movement->total_amount ? Yii::$app->formatter->asDecimal($movement->total_amount, 2) : '-' ?>
                                    </td>
                                    <td><?= Html::encode($movement->comment) ?></td>
                                    <td><?= $movement->createdBy ? Html::encode($movement->createdBy->username) : '-' ?></td>
                                </tr>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>