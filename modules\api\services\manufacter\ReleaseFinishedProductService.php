<?php

namespace app\modules\api\services\manufacter;

use Yii;
use app\common\models\ActionLogger;
use app\common\models\Material;
use app\common\models\MaterialProduction;
use app\common\models\Product;
use app\common\models\ProductDefect;
use app\common\models\ProductIngredients;
use app\common\models\ProductStorage;
use app\common\models\ProductStorageHistory;
use app\common\models\ProductStorageHistoryMaterials;
use app\common\models\Tracking;
use yii\base\Component;
use yii\log\Logger;
use yii\helpers\VarDumper;

/**
 * Сервис для работы с выпуском готовой продукции
 */
class ReleaseFinishedProductService extends Component
{
    /**
     * Выпуск готовой продукции
     *
     * @param int $productId ID продукта
     * @param int|float $quantity Количество продукта
     * @return array Результат операции
     * @throws \Exception
     */
    public function releaseFinishedProduct($productId, $quantity)
    {
        
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $product = Product::findOne([
                'id' => $productId,
                'deleted_at' => null
            ]);

            if (!$product) {
                throw new \Exception('Продукт не найден');
            }

            $repackagingProducts = $this->getRepackagingProducts($productId);
            $repackagingQuantity = (float)$this->calculateRepackagingQuantity($repackagingProducts);
            
            $originalQuantity = (float)$quantity;
            if ($repackagingQuantity > 0) {
                if ($repackagingQuantity >= $originalQuantity) {
                    $quantity = 0; // Явно устанавливаем в 0, а не вычитаем
                } else {
                    $quantity = $originalQuantity - $repackagingQuantity;
                }

                $this->logRepackagingUsage($productId, $repackagingQuantity, $originalQuantity, $quantity);
            }

            // Используем epsilon для сравнения с нулем
            $epsilon = 0.0001;

            if ($quantity > $epsilon) {
                // Получаем основные ингредиенты продукта
                $productIngredients = ProductIngredients::find()
                    ->where([
                        'product_id' => $productId,
                        'end_date' => '9999-12-31',
                        'is_alternative' => false
                    ])
                    ->all();

                if (empty($productIngredients)) {
                    throw new \Exception(Yii::t('app', 'for_this_product_no_main_ingredients'));
                }

                // Проверяем наличие материалов и используем альтернативные при необходимости
                $usedMaterials = [];
                $usedAlternatives = [];

                foreach ($productIngredients as $ingredient) {
                    $materialId = $ingredient->material_id;
                    $requiredQuantity = (float)$quantity;
                    
                    $ingredientMaterial = Material::findOne($materialId);
                    $materialName = $ingredientMaterial ? $ingredientMaterial->name : "Материал #{$materialId}";
                    $materialCategory = $ingredientMaterial ? $ingredientMaterial->category_id : 'Не указана';
                    $materialUnitType = $ingredientMaterial ? $ingredientMaterial->unit_type : 'Не указан';
                    
                    // Проверяем наличие основного материала
                    $materialProductions = MaterialProduction::find()
                        ->where([
                            'material_id' => $materialId,
                            'deleted_at' => null
                        ])
                        ->andWhere(['>', 'quantity', 0])
                        ->orderBy(['created_at' => SORT_ASC])
                        ->all();

                    $totalAvailable = array_sum(array_column($materialProductions, 'quantity'));
                    
                    // Если основного материала достаточно, используем только его
                    if ($totalAvailable >= $requiredQuantity) {
                        // Используем только основной материал
                        $usedMaterials[$materialId] = [
                            'material_id' => $materialId,
                            'quantity' => $requiredQuantity,
                            'is_alternative' => false
                        ];
                        
                    } else {
                        // Если основного материала недостаточно, используем сколько есть,
                        // а остальное берем из альтернативного материала
                        $remainingRequired = $requiredQuantity - $totalAvailable;
                        // Получаем информацию об основном материале
                        $originalMaterial = Material::findOne($materialId);
                        $originalCategoryId = $originalMaterial ? $originalMaterial->category_id : null;
                        $originalUnitType = $originalMaterial ? $originalMaterial->unit_type : null;
                        $originalName = $originalMaterial ? $originalMaterial->name : "Материал #{$materialId}";
                        
                        // Ищем альтернативный материал для оставшегося количества
                        $alternativeMaterialId = ProductIngredients::getAlternativeMaterial($productId, $materialId, $remainingRequired);

                        if ($alternativeMaterialId) {
                            // Получаем данные об альтернативном материале
                            $altMaterial = Material::findOne($alternativeMaterialId);
                            $altCategoryId = $altMaterial ? $altMaterial->category_id : null;
                            $altUnitType = $altMaterial ? $altMaterial->unit_type : null;
                            $altName = $altMaterial ? $altMaterial->name : "Материал #{$alternativeMaterialId}";
                            
                            // Проверка совпадения категорий материалов
                            if ($altMaterial && $originalMaterial && $altMaterial->category_id != $originalMaterial->category_id) {
                                throw new \Exception(Yii::t('app', 'Not enough material "{material_name}" in production and no alternative available. Available: {available}, Required: {required}', [
                                    'material_name' => $materialName,
                                    'available' => $totalAvailable,
                                    'required' => $requiredQuantity,
                                ]));
                            }
                            
                            // Проверяем, достаточно ли альтернативного материала
                            $altMaterialProductions = MaterialProduction::find()
                                ->where([
                                    'material_id' => $alternativeMaterialId,
                                    'deleted_at' => null
                                ])
                                ->andWhere(['>', 'quantity', 0])
                                ->all();

                            $altTotalAvailable = array_sum(array_column($altMaterialProductions, 'quantity'));
                            
                            if ($altTotalAvailable < $remainingRequired) {
                                // Если альтернативного материала недостаточно, выбрасываем исключение
                                
                                $originalName = $originalMaterial ? $originalMaterial->name : "Материал #{$materialId}";
                                $altName = $altMaterial ? $altMaterial->name : "Материал #{$alternativeMaterialId}";

                                throw new \Exception(Yii::t('app', 'Not enough alternative material "{alt_name}" for "{original_name}". Available: {available}, Required: {required}', [
                                    'alt_name' => $altName,
                                    'original_name' => $originalName,
                                    'available' => $altTotalAvailable,
                                    'required' => $remainingRequired,
                                ]));
                            }

                            // Материалы уже загружены и проверены выше

                            // Используем основной материал (сколько есть)
                            if ($totalAvailable > 0) {
                                $usedMaterials[$materialId] = [
                                    'material_id' => $materialId,
                                    'quantity' => $totalAvailable,
                                    'is_alternative' => false
                                ];
                            }

                            // Используем альтернативный материал для оставшегося количества
                            $altMaterialKey = 'alt_' . $materialId . '_' . $alternativeMaterialId;
                            $usedMaterials[$altMaterialKey] = [
                                'material_id' => $alternativeMaterialId,
                                'quantity' => $remainingRequired,
                                'is_alternative' => true,
                                'original_material_id' => $materialId,
                                'original_name' => $originalMaterial ? $originalMaterial->name : "Материал #{$materialId}",
                                'alternative_name' => $altMaterial ? $altMaterial->name : "Материал #{$alternativeMaterialId}"
                            ];
                            
                            $usedAlternatives[$materialId] = [
                                'alt_id' => $alternativeMaterialId,
                                'original_name' => $originalMaterial ? $originalMaterial->name : "Материал #{$materialId}",
                                'alternative_name' => $altMaterial ? $altMaterial->name : "Материал #{$alternativeMaterialId}",
                                'original_quantity' => $totalAvailable,
                                'alternative_quantity' => $remainingRequired
                            ];
                        } else {
                            // Если альтернативы нет, выбрасываем исключение
                            $materialName = Material::findOne($materialId) ? Material::findOne($materialId)->name : "Материал #{$materialId}";
                            throw new \Exception(Yii::t('app', 'Not enough material "{material_name}" in production and no alternative available. Available: {available}, Required: {required}', [
                                'material_name' => $materialName,
                                'available' => $totalAvailable,
                                'required' => $requiredQuantity,
                            ]));
                        }
                    }
                }

                // Логируем использование альтернативных материалов
                if (!empty($usedAlternatives)) {
                    $this->logAlternativeMaterialsUsage($productId, $usedAlternatives);
                }

                // Списываем материалы (основные или альтернативные)
                $this->deductMaterials($usedMaterials);
            } else {
                // Если quantity близко к нулю, явно устанавливаем его в 0
                $quantity = 0;
              
            }

            // Создаем запись о выпуске продукции
            $productStorage = $this->createProductStorage($productId, $originalQuantity);

            // Создаем запись в истории склада
            $storageHistoryId = $this->createProductStorageHistory($productStorage->id, $productId, $originalQuantity);
            
            // Сохраняем информацию о списанных материалах
            if (!empty($usedMaterials)) {
                $this->saveUsedMaterialsHistory($storageHistoryId, $usedMaterials);
            }

            // Помечаем использованные продукты для переупаковки как удаленные
            if (isset($repackagingQuantity) && $repackagingQuantity > 0) {
                $this->markRepackagingProductsAsDeleted($repackagingProducts);
            }

            // Логируем выпуск продукции
            $logData = [
                'product_id' => $productId,
                'total_quantity' => $originalQuantity,
                'new_production_quantity' => $quantity,
            ];

            if (isset($repackagingQuantity) && $repackagingQuantity > 0) {
                $logData['repackaging_quantity'] = $repackagingQuantity;
                $logData['repackaging_product_ids'] = array_map(function($product) {
                    return $product->id;
                }, $repackagingProducts);
            }

            ActionLogger::actionLog(
                'release_finished_product',
                'product_storage',
                $productStorage->id,
                $logData
            );

            // Создаем запись в tracking
            $tracking = $this->createTracking($productStorage->id);

            $transaction->commit();
            
            return [
                'success' => true,
                'message' => 'Продукт успешно выпущен',
                'product_storage_id' => $productStorage->id
            ];

        } catch (\Exception $e) {
            $transaction->rollBack();
            
            return [
                'success' => false,
                'message' => 'Ошибка при выпуске продукта',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Получение продуктов для переупаковки
     *
     * @param int $productId ID продукта
     * @return array Продукты для переупаковки
     */
    protected function getRepackagingProducts($productId)
    {
        return ProductDefect::find()
            ->where([
                'product_id' => $productId,
                'is_repackaging' => true,
                'deleted_at' => null
            ])
            ->andWhere(['IS NOT', 'accepted_at', null])
            ->andWhere(['IS NOT', 'accepted_user_id', null])
            ->orderBy(['created_at' => SORT_ASC])
            ->all();
    }

    /**
     * Расчет количества продуктов для переупаковки
     *
     * @param array $repackagingProducts Продукты для переупаковки
     * @return int Количество продуктов для переупаковки
     */
    protected function calculateRepackagingQuantity($repackagingProducts)
    {
        $repackagingQuantity = 0;
        foreach ($repackagingProducts as $repackagingProduct) {
            $repackagingQuantity += $repackagingProduct->quantity;
        }
        return $repackagingQuantity;
    }

    /**
     * Логирование использования продуктов для переупаковки
     *
     * @param int $productId ID продукта
     * @param int $repackagingQuantity Количество продуктов для переупаковки
     * @param int $originalQuantity Исходное количество
     * @param int $productionQuantity Количество для производства
     */
    protected function logRepackagingUsage($productId, $repackagingQuantity, $originalQuantity, $productionQuantity)
    {
        ActionLogger::actionLog(
            'use_repackaging_products',
            'product_defect',
            null,
            [
                'product_id' => $productId,
                'repackaging_quantity' => $repackagingQuantity,
                'original_quantity' => $originalQuantity,
                'production_quantity' => $productionQuantity
            ]
        );
    }

    /**
     * Логирование использования альтернативных материалов
     *
     * @param int $productId ID продукта
     * @param array $usedAlternatives Использованные альтернативные материалы
     */
    protected function logAlternativeMaterialsUsage($productId, $usedAlternatives)
    {
        ActionLogger::actionLog(
            'use_alternative_materials',
            'product_storage',
            null,
            [
                'product_id' => $productId,
                'alternatives' => $usedAlternatives
            ]
        );
    }

    /**
     * Списание материалов
     *
     * @param array $usedMaterials Использованные материалы
     * @throws \Exception
     */
    protected function deductMaterials($usedMaterials)
    {
        foreach ($usedMaterials as $materialKey => $materialData) {
            $materialId = $materialData['material_id'];
            $requiredQuantity = $materialData['quantity'];
            $isAlternative = isset($materialData['is_alternative']) && $materialData['is_alternative'];
            $originalMaterialId = isset($materialData['original_material_id']) ? $materialData['original_material_id'] : null;
            
            $materialName = Material::findOne($materialId) ? Material::findOne($materialId)->name : "Материал #{$materialId}";
            $logPrefix = $isAlternative ? "Альтернативный" : "Основной";
            
            $materialProductions = MaterialProduction::find()
                ->where([
                    'material_id' => $materialId,
                    'deleted_at' => null
                ])
                ->andWhere(['>', 'quantity', 0])
                ->orderBy(['created_at' => SORT_ASC])
                ->all();
                
            $totalAvailable = array_sum(array_column($materialProductions, 'quantity'));

            $remainingQuantity = $requiredQuantity;
            foreach ($materialProductions as $production) {
                if ($remainingQuantity <= 0) break;

                $quantityToDeduct = min($production->quantity, $remainingQuantity);
                $oldQuantity = $production->quantity;
                $production->quantity -= $quantityToDeduct;
                $remainingQuantity -= $quantityToDeduct;
                
                if (!$production->save()) {
                    throw new \Exception('Ошибка при обновлении количества в производстве');
                }
            }
        }
    }

    /**
     * Создание записи о выпуске продукции
     *
     * @param int $productId ID продукта
     * @param int $quantity Количество
     * @return ProductStorage
     * @throws \Exception
     */
    protected function createProductStorage($productId, $quantity)
    {
        $productStorage = new ProductStorage();
        $productStorage->product_id = $productId;
        $productStorage->add_user_id = Yii::$app->user->getId();
        $productStorage->quantity = $quantity;
        $productStorage->enter_date = date('Y-m-d H:i:s');

        if (!$productStorage->save()) {
            throw new \Exception('Ошибка при сохранении на складе: ' . json_encode($productStorage->getErrors()));
        }

        return $productStorage;
    }

    /**
     * Создание записи в истории склада
     *
     * @param int $productStorageId ID записи о выпуске продукции
     * @param int $productId ID продукта
     * @param int $quantity Количество
     * @return int ID созданной записи истории
     * @throws \Exception
     */
    protected function createProductStorageHistory($productStorageId, $productId, $quantity)
    {
        $storageHistory = new ProductStorageHistory();
        $storageHistory->product_storage_id = $productStorageId;
        $storageHistory->product_id = $productId;
        $storageHistory->quantity = $quantity;
        $storageHistory->type = ProductStorageHistory::TYPE_INCOME;
        $storageHistory->created_at = date('Y-m-d H:i:s');
        $storageHistory->add_user_id = Yii::$app->user->getId();

        if (!$storageHistory->save()) {
            throw new \Exception('Ошибка сохранения истории склада');
        }
        
        return $storageHistory->id;
    }

    /**
     * Сохранение информации о списанных материалах в историю
     *
     * @param int $storageHistoryId ID записи истории склада
     * @param array $usedMaterials Информация о списанных материалах
     * @throws \Exception
     */
    protected function saveUsedMaterialsHistory($storageHistoryId, $usedMaterials)
    {
        foreach ($usedMaterials as $materialKey => $materialData) {
            $material = new ProductStorageHistoryMaterials();
            $material->product_storage_history_id = $storageHistoryId;
            $material->material_id = $materialData['material_id'];
            $material->quantity = $materialData['quantity'];
            $material->created_at = date('Y-m-d H:i:s');
            
            if (isset($materialData['is_alternative']) && $materialData['is_alternative']) {
                $material->is_alternative = true;
                if (isset($materialData['original_material_id'])) {
                    $material->original_material_id = $materialData['original_material_id'];
                }
            } else {
                $material->is_alternative = false;
            }
            
            // Проверяем соответствие модели перед сохранением
            if (!$material->validate()) {
                throw new \Exception('Ошибка валидации при сохранении истории материалов: ' . json_encode($material->getErrors()));
            }
            
            if (!$material->save()) {
                throw new \Exception('Ошибка при сохранении истории материалов');
            }
            
        }
    }
    
    /**
     * Пометка продуктов для переупаковки как удаленных
     *
     * @param array $repackagingProducts Продукты для переупаковки
     * @throws \Exception
     */
    protected function markRepackagingProductsAsDeleted($repackagingProducts)
    {
        foreach ($repackagingProducts as $repackagingProduct) {
            $repackagingProduct->deleted_at = date('Y-m-d H:i:s');
            if (!$repackagingProduct->save()) {
                throw new \Exception('Ошибка при обновлении записи о переупаковке');
            }
        }
    }

    /**
     * Создание записи в tracking
     *
     * @param int $productStorageId ID записи о выпуске продукции
     * @return Tracking
     * @throws \Exception
     */
    protected function createTracking($productStorageId)
    {
        $tracking = new Tracking();
        $tracking->progress_type = Tracking::TYPE_PRODUCT_RELEASE;
        $tracking->process_id = $productStorageId;
        $tracking->created_at = date('Y-m-d H:i:s');
        $tracking->status = Tracking::STATUS_NOT_ACCEPTED;
        $tracking->accepted_at = null;

        if (!$tracking->save()) {
            throw new \Exception('Ошибка при сохранении tracking');
        }

        return $tracking;
    }
}

