<?php
use yii\helpers\Html;
use app\assets\Select2Asset;

Select2Asset::register($this);
?>

<form id="part-reserve-form">
    <input type="hidden" name="equipment_id" value="<?= $equipment->id ?>">
    <input type="hidden" name="part_id" value="<?= $part->id ?>">

    <div class="form-group">
        <label class="form-label"><?= Yii::t('app', 'part_name') ?></label>
        <p><strong><?= Html::encode($part->name) ?></strong></p>
    </div>

    <div class="form-group">
        <label class="form-label"><?= Yii::t('app', 'assigned_quantity') ?></label>
        <p>
            <strong><?= Yii::t('app', 'active') ?>:</strong> <?= $assignment->active_quantity ?? 0 ?> |
            <strong><?= Yii::t('app', 'repair') ?>:</strong> <?= $assignment->repair_quantity ?? 0 ?>
        </p>
    </div>

    <div class="form-group">
        <label class="form-label"><?= Yii::t('app', 'quantity') ?> <span class="text-danger">*</span></label>
        <input type="number" class="form-control" name="quantity" min="1" max="<?= $assignment->quantity ?>" value="<?= $assignment->quantity ?>" required>
        <div class="invalid-feedback" id="quantity-error"></div>
    </div>


    <div class="form-group">
        <label class="form-label"><?= Yii::t('app', 'comment') ?></label>
        <textarea class="form-control" name="comment" rows="3" placeholder="<?= Yii::t('app', 'optional_comment') ?>"></textarea>
        <div class="invalid-feedback" id="comment-error"></div>
    </div>
</form>

<script>
$(document).ready(function() {
    // Валидация количества в реальном времени
    $('input[name="quantity"]').on('input', function() {
        var quantity = parseInt($(this).val());
        var maxQuantity = parseInt($(this).attr('max'));
        var errorDiv = $('#quantity-error');

        $(this).removeClass('is-invalid');
        errorDiv.text('');

        if (quantity <= 0) {
            $(this).addClass('is-invalid');
            errorDiv.text('<?= Yii::t('app', 'quantity_required_positive') ?>');
        } else if (quantity > maxQuantity) {
            $(this).addClass('is-invalid');
            errorDiv.text('<?= Yii::t('app', 'reserve_quantity_exceeds_assigned', ['available' => '']) ?>' + maxQuantity);
        }
    });
});
</script>
