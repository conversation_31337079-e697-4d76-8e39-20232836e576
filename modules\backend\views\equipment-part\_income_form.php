<?php
use yii\helpers\Html;
use app\assets\Select2Asset;

Select2Asset::register($this);
?>

<form id="part-income-form">
    <input type="hidden" name="id" value="<?= $model->id ?>">

    <div class="form-group">
        <label class="form-label"><?= Yii::t('app', 'part_name') ?></label>
        <p><strong><?= Html::encode($model->name) ?></strong></p>
    </div>

    <div class="form-group">
        <label class="form-label"><?= Yii::t('app', 'income_quantity') ?></label>
        <input type="number" class="form-control" name="quantity" min="1" required>
        <div class="invalid-feedback" id="quantity-error"></div>
    </div>

    <div class="form-group">
        <label class="form-label"><?= Yii::t('app', 'price_per_unit') ?></label>
        <input type="number" class="form-control" name="price_per_unit" step="0.01" min="0">
        <div class="invalid-feedback" id="price_per_unit-error"></div>
    </div>

    <div class="form-group">
        <label class="form-label"><?= Yii::t('app', 'comment') ?></label>
        <textarea class="form-control" name="comment" rows="3"></textarea>
        <div class="invalid-feedback" id="comment-error"></div>
    </div>
</form>
