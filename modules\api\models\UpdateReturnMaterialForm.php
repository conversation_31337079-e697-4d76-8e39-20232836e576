<?php

namespace app\modules\api\models;

use Yii;
use yii\base\Model;
use app\common\models\Material;
use app\common\models\Supplier;

/**
 * Форма для валидации обновления возврата материалов
 */
class UpdateReturnMaterialForm extends Model
{
    public $group_id;
    public $materials;
    public $supplier_id;
    public $description;

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['group_id', 'materials', 'supplier_id'], 'required'],
            ['group_id', 'integer', 'min' => 1],
            ['materials', 'validateMaterials'],
            ['supplier_id', 'integer', 'min' => 1],
            ['supplier_id', 'exist', 'targetClass' => Supplier::class, 'targetAttribute' => 'id'],
            ['description', 'string', 'max' => 1000],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'group_id' => Yii::t('app', 'group_id'),
            'materials' => Yii::t('app', 'materials'),
            'supplier_id' => Yii::t('app', 'supplier'),
            'description' => Yii::t('app', 'description'),
        ];
    }

    /**
     * Валидация массива материалов
     */
    public function validateMaterials($attribute, $params)
    {
        if (!is_array($this->materials)) {
            $this->addError($attribute, 'Материалы должны быть массивом');
            return;
        }

        if (empty($this->materials)) {
            $this->addError($attribute, 'Необходимо указать хотя бы один материал');
            return;
        }

        $materialIds = [];
        foreach ($this->materials as $index => $material) {
            if (!isset($material['material_id']) || !isset($material['quantity'])) {
                $this->addError($attribute, "Материал #{$index} должен содержать material_id и quantity");
                continue;
            }

            if (!is_numeric($material['material_id']) || $material['material_id'] <= 0) {
                $this->addError($attribute, "Неверный material_id в материале #{$index}");
                continue;
            }

            // Проверяем на дублирование материалов
            if (in_array($material['material_id'], $materialIds)) {
                $this->addError($attribute, "Материал с ID {$material['material_id']} указан несколько раз");
                continue;
            }
            $materialIds[] = $material['material_id'];

            // Проверяем, что количество является числом и больше 0
            if (!is_numeric($material['quantity']) || floatval($material['quantity']) <= 0) {
                $this->addError($attribute, "Количество должно быть больше 0 в материале #{$index}");
                continue;
            }

            // Получаем тип единицы измерения материала
            $materialModel = Material::findOne($material['material_id']);
            if (!$materialModel) {
                $this->addError($attribute, "Материал с ID {$material['material_id']} не найден");
                continue;
            }

            // Для штук (UNIT_TYPE_PIECE) проверяем, что количество целое
            if ($materialModel->unit_type == Material::UNIT_TYPE_PIECE && 
                floor(floatval($material['quantity'])) != floatval($material['quantity'])) {
                $this->addError($attribute, "Для штучных материалов количество должно быть целым числом в материале #{$index}");
            }
        }
    }

    /**
     * Преобразование данных формы в формат ReturnMaterialForm
     * 
     * @return ReturnMaterialForm
     */
    public function toReturnMaterialForm()
    {
        $form = new ReturnMaterialForm();
        $form->materials = $this->materials;
        $form->supplier_id = $this->supplier_id;
        $form->description = $this->description;
        
        return $form;
    }
}
