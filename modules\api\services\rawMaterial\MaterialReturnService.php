<?php

namespace app\modules\api\services\rawMaterial;

use Yii;
use app\common\models\ActionLogger;
use app\common\models\MaterialStatusGroup;
use app\common\models\MaterialStatus;
use app\common\models\Tracking;
use app\modules\api\models\ReturnMaterialForm;
use yii\base\Component;

/**
 * Сервис для создания возврата материалов на склад
 */
class MaterialReturnService extends Component
{
    /**
     * Создание возврата материалов на склад
     *
     * @param ReturnMaterialForm $form Валидированная форма с данными возврата
     * @return array Результат операции
     */
    public function createMaterialReturn(ReturnMaterialForm $form)
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            // Создаем группу материалов для возврата
            $group = new MaterialStatusGroup();
            $group->add_user_id = Yii::$app->user->id;
            $group->status = MaterialStatusGroup::STATUS_RETURNED_TO_SUPPLIER;
            $group->supplier_id = $form->supplier_id;
            $group->created_at = date('Y-m-d H:i:s');
            // НЕ устанавливаем accepted_at и accepted_user_id - оставляем null для последующего подтверждения

            if (!$group->save()) {
                throw new \Exception('Ошибка сохранения группы материалов: ' . json_encode($group->getErrors()));
            }

            // Создаем записи для каждого материала
            foreach ($form->materials as $materialData) {
                $materialStatus = new MaterialStatus();
                $materialStatus->status_group_id = $group->id;
                $materialStatus->material_id = $materialData['material_id'];
                $materialStatus->quantity = $materialData['quantity'];
                $materialStatus->created_at = date('Y-m-d H:i:s');

                if (!$materialStatus->save()) {
                    throw new \Exception('Ошибка сохранения материала: ' . json_encode($materialStatus->getErrors()));
                }
            }

            // Создаем трекинг для отслеживания статуса возврата
            $tracking = new Tracking();
            $tracking->progress_type = Tracking::TYPE_MATERIAL_RETURN;
            $tracking->process_id = $group->id;
            $tracking->created_at = date('Y-m-d H:i:s');
            $tracking->status = Tracking::STATUS_NOT_ACCEPTED;
            // accepted_at остается null до подтверждения

            if (!$tracking->save()) {
                throw new \Exception('Ошибка сохранения tracking: ' . json_encode($tracking->getErrors()));
            }

            // Логируем действие
            ActionLogger::actionLog(
                'return_material',
                'material_status_group',
                $group->id,
                [
                    'supplier_id' => $form->supplier_id,
                    'materials' => array_map(function($material) {
                        return [
                            'material_id' => $material['material_id'],
                            'quantity' => $material['quantity']
                        ];
                    }, $form->materials),
                    'description' => $form->description
                ]
            );

            $transaction->commit();

            return [
                'success' => true,
                'message' => 'Материалы успешно отправлены на возврат. Ожидается подтверждение.',
                'group_id' => $group->id
            ];

        } catch (\Exception $e) {
            $transaction->rollBack();
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
}
