<?php

namespace app\common\models;

use Yii;
use yii\db\ActiveRecord;
use app\modules\backend\models\Users;

/**
 * This is the model class for table "equipment_part_movements".
 *
 * @property int $id
 * @property int $equipment_part_id
 * @property int $movement_type
 * @property int $quantity
 * @property float|null $price_per_unit
 * @property float|null $total_amount
 * @property string|null $comment
 * @property string $created_at
 * @property int|null $created_by
 *
 * @property EquipmentPart $equipmentPart
 * @property Users $createdBy
 */
class EquipmentPartMovement extends ActiveRecord
{
    // Типы движений
    const MOVEMENT_INCOME = 1;    // Приход
    const MOVEMENT_OUTCOME = 2;   // Расход
    const MOVEMENT_DEFECT = 3;    // Брак

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'equipment_part_movements';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['equipment_part_id', 'movement_type', 'quantity'], 'required'],
            [['equipment_part_id', 'movement_type', 'quantity', 'created_by'], 'integer'],
            [['price_per_unit', 'total_amount'], 'number'],
            [['comment'], 'string'],
            [['created_at'], 'safe'],
            [['quantity'], 'integer', 'min' => 1],
            [['movement_type'], 'in', 'range' => [self::MOVEMENT_INCOME, self::MOVEMENT_OUTCOME, self::MOVEMENT_DEFECT]],
            [['equipment_part_id'], 'exist', 'skipOnError' => true, 'targetClass' => EquipmentPart::class, 'targetAttribute' => ['equipment_part_id' => 'id']],
            [['created_by'], 'exist', 'skipOnError' => true, 'targetClass' => Users::class, 'targetAttribute' => ['created_by' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'equipment_part_id' => Yii::t('app', 'Equipment Part'),
            'movement_type' => Yii::t('app', 'Movement Type'),
            'quantity' => Yii::t('app', 'Quantity'),
            'price_per_unit' => Yii::t('app', 'Price Per Unit'),
            'total_amount' => Yii::t('app', 'Total Amount'),
            'comment' => Yii::t('app', 'Comment'),
            'created_at' => Yii::t('app', 'Created At'),
            'created_by' => Yii::t('app', 'Created By'),
        ];
    }

    /**
     * Gets query for [[EquipmentPart]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getEquipmentPart()
    {
        return $this->hasOne(EquipmentPart::class, ['id' => 'equipment_part_id']);
    }

    /**
     * Gets query for [[CreatedBy]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getCreatedBy()
    {
        return $this->hasOne(Users::class, ['id' => 'created_by']);
    }

    /**
     * Get movement type labels
     * @return array
     */
    public static function getMovementTypeLabels()
    {
        return [
            self::MOVEMENT_INCOME => Yii::t('app', 'income'),
            self::MOVEMENT_OUTCOME => Yii::t('app', 'outcome'),
            self::MOVEMENT_DEFECT => Yii::t('app', 'defect'),
        ];
    }

    /**
     * Get movement type label
     * @param int $type
     * @return string
     */
    public static function getMovementTypeLabel($type)
    {
        $labels = self::getMovementTypeLabels();
        return $labels[$type] ?? Yii::t('app', 'unknown');
    }

    /**
     * Before save event
     * @param bool $insert
     * @return bool
     */
    public function beforeSave($insert)
    {
        if (parent::beforeSave($insert)) {
            // Автоматически рассчитываем общую сумму
            if ($this->price_per_unit && $this->quantity) {
                $this->total_amount = $this->price_per_unit * $this->quantity;
            }

            // Устанавливаем пользователя
            if ($insert && !$this->created_by) {
                $this->created_by = Yii::$app->user->id;
            }

            return true;
        }
        return false;
    }

    /**
     * After save event
     * @param bool $insert
     * @param array $changedAttributes
     */
    public function afterSave($insert, $changedAttributes)
    {
        parent::afterSave($insert, $changedAttributes);

        if ($insert) {
            // Обновляем количество в основной таблице запчастей
            $this->updatePartQuantity();
        }
    }

    /**
     * Update part quantity based on movement
     */
    private function updatePartQuantity()
    {
        $part = $this->equipmentPart;
        if ($part) {
            switch ($this->movement_type) {
                case self::MOVEMENT_INCOME:
                    $part->quantity += $this->quantity;
                    break;
                case self::MOVEMENT_OUTCOME:
                case self::MOVEMENT_DEFECT:
                    $part->quantity -= $this->quantity;
                    break;
            }

            // Обновляем дату последней покупки и цену при приходе
            if ($this->movement_type == self::MOVEMENT_INCOME) {
                $part->last_purchase_date = date('Y-m-d');
                // Обновляем цену запчасти только если указана цена за единицу
                // Если цена не передана (null или 0), оставляем старую цену
                if ($this->price_per_unit !== null && $this->price_per_unit > 0) {
                    $part->price = $this->price_per_unit;
                }
            }

            $part->save(false); // Сохраняем без валидации
        }
    }
}
