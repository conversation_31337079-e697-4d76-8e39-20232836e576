<?php
use yii\helpers\Html;
use app\common\models\EquipmentPart;
?>

<form id="equipment-decommission-form">
    <div class="alert alert-warning">
        <?= Yii::t('app', 'You are about to decommission this equipment. This action cannot be undone.') ?>
    </div>

    <div class="form-group">
        <label class="form-label"><?= Yii::t('app', 'Equipment') ?></label>
        <p><strong><?= Html::encode($model->name) ?></strong></p>
    </div>

    <?php if (!empty($parts)): ?>
        <div class="form-group">
            <label class="form-label"><?= Yii::t('app', 'Parts attached to this equipment') ?></label>
            <table class="table table-bordered table-striped">
                <thead>
                    <tr>
                        <th><?= Yii::t('app', 'Name') ?></th>
                        <th><?= Yii::t('app', 'Action') ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($parts as $part): ?>
                        <tr>
                            <td><?= Html::encode($part->name) ?></td>
                            <td>
                                <select class="form-control" name="parts[<?= $part->id ?>]">
                                    <option value="0"><?= Yii::t('app', 'Decommission with equipment') ?></option>
                                    <option value="1"><?= Yii::t('app', 'Move to reserve') ?></option>
                                </select>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <div class="alert alert-info">
            <?= Yii::t('app', 'No parts are attached to this equipment.') ?>
        </div>
    <?php endif; ?>

    <div class="form-group">
        <label class="form-label"><?= Yii::t('app', 'Comment') ?></label>
        <textarea class="form-control" name="comment" rows="3"></textarea>
        <div class="invalid-feedback" id="comment-error"></div>
    </div>
</form>

<style>
.invalid-feedback {
    display: none;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #dc3545;
}

.is-invalid ~ .invalid-feedback {
    display: block;
}

.form-control.is-invalid {
    border-color: #dc3545;
}
</style>
