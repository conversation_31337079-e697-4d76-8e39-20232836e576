<?php

namespace app\modules\api\services\rawMaterial;

use Yii;
use app\common\models\ActionLogger;
use app\common\models\MaterialStatusGroup;
use app\common\models\MaterialStatus;
use app\common\models\Material;
use app\modules\api\models\ReturnMaterialForm;
use yii\base\Component;

/**
 * Сервис для управления возвратами материалов (обновление и удаление)
 */
class MaterialReturnManagementService extends Component
{
    /**
     * Получение данных возврата материалов для редактирования
     *
     * @param int $groupId ID группы возврата
     * @return array Результат операции
     */
    public function getReturnMaterialForUpdate($groupId)
    {
        try {
            // Находим группу возврата
            $materialStatusGroup = MaterialStatusGroup::findOne([
                'id' => $groupId,
                'status' => MaterialStatusGroup::STATUS_RETURNED_TO_SUPPLIER,
                'deleted_at' => null
            ]);

            if (!$materialStatusGroup) {
                return [
                    'success' => false,
                    'message' => 'Группа возврата материалов не найдена'
                ];
            }

            // Проверяем, что группа еще не подтверждена
            if ($materialStatusGroup->accepted_user_id !== null || $materialStatusGroup->accepted_at !== null) {
                return [
                    'success' => false,
                    'message' => 'Нельзя редактировать подтвержденный возврат'
                ];
            }

            // Проверка авторства - только создатель может редактировать
            if ($materialStatusGroup->add_user_id != Yii::$app->user->id) {
                return [
                    'success' => false,
                    'message' => 'Вы можете редактировать только свои записи'
                ];
            }

            // Получаем материалы из группы
            $materials = MaterialStatus::find()
                ->select([
                    'material_status.id',
                    'material_status.material_id',
                    'to_char(material_status.quantity, \'FM999999999.########\') as quantity',
                    'm.name as material_name',
                    'm.unit_type'
                ])
                ->leftJoin('material m', 'material_status.material_id = m.id')
                ->where([
                    'material_status.status_group_id' => $groupId,
                    'material_status.deleted_at' => null
                ])
                ->asArray()
                ->all();

            // Добавляем название единицы измерения для каждого материала
            foreach ($materials as &$material) {
                $material['unit_type_name'] = Material::getUnitTypeName($material['unit_type']);
            }

            return [
                'success' => true,
                'data' => [
                    'group_id' => $materialStatusGroup->id,
                    'supplier_id' => $materialStatusGroup->supplier_id,
                    'created_at' => $materialStatusGroup->created_at,
                    'materials' => $materials
                ]
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Обновление возврата материалов
     *
     * @param int $groupId ID группы возврата
     * @param ReturnMaterialForm $form Валидированная форма с новыми данными
     * @return array Результат операции
     */
    public function updateMaterialReturn($groupId, ReturnMaterialForm $form)
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            // Проверяем возможность редактирования
            $checkResult = $this->checkEditPermission($groupId);
            if (!$checkResult['success']) {
                return $checkResult;
            }

            $materialStatusGroup = $checkResult['group'];

            // Обновляем данные группы
            $materialStatusGroup->supplier_id = $form->supplier_id;
            
            if (!$materialStatusGroup->save()) {
                throw new \Exception('Ошибка обновления группы возврата: ' . json_encode($materialStatusGroup->getErrors()));
            }

            // Удаляем старые записи материалов (soft delete)
            MaterialStatus::deleteAll(
                [
                    'status_group_id' => $groupId,
                    'deleted_at' => null
                ]
            );

            // Создаем новые записи материалов
            foreach ($form->materials as $materialData) {
                $materialStatus = new MaterialStatus();
                $materialStatus->material_id = $materialData['material_id'];
                $materialStatus->status_group_id = $groupId;
                $materialStatus->quantity = $materialData['quantity'];
                $materialStatus->created_at = date('Y-m-d H:i:s');

                if (!$materialStatus->save()) {
                    throw new \Exception('Ошибка сохранения материала: ' . json_encode($materialStatus->getErrors()));
                }
            }

            // Логируем действие
            ActionLogger::actionLog(
                'update_return_material',
                'material_status_group',
                $groupId,
                [
                    'supplier_id' => $form->supplier_id,
                    'materials' => array_map(function($material) {
                        return [
                            'material_id' => $material['material_id'],
                            'quantity' => $material['quantity']
                        ];
                    }, $form->materials),
                    'description' => $form->description
                ]
            );

            $transaction->commit();

            return [
                'success' => true,
                'message' => 'Возврат материалов успешно обновлен'
            ];

        } catch (\Exception $e) {
            $transaction->rollBack();
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Удаление возврата материалов
     *
     * @param int $groupId ID группы возврата
     * @return array Результат операции
     */
    public function deleteMaterialReturn($groupId)
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            // Проверяем возможность удаления
            $checkResult = $this->checkEditPermission($groupId);
            if (!$checkResult['success']) {
                return $checkResult;
            }

            $materialStatusGroup = $checkResult['group'];

            // Удаляем записи материалов (soft delete)
            MaterialStatus::updateAll(
                ['deleted_at' => date('Y-m-d H:i:s')],
                [
                    'status_group_id' => $groupId,
                    'deleted_at' => null
                ]
            );

            // Удаляем группу возврата (soft delete)
            $materialStatusGroup->deleted_at = date('Y-m-d H:i:s');
            
            if (!$materialStatusGroup->save()) {
                throw new \Exception('Ошибка удаления группы возврата: ' . json_encode($materialStatusGroup->getErrors()));
            }

            // Логируем действие
            ActionLogger::actionLog(
                'delete_return_material',
                'material_status_group',
                $groupId,
                [
                    'supplier_id' => $materialStatusGroup->supplier_id,
                    'deleted_at' => $materialStatusGroup->deleted_at
                ]
            );

            $transaction->commit();

            return [
                'success' => true,
                'message' => 'Возврат материалов успешно удален'
            ];

        } catch (\Exception $e) {
            $transaction->rollBack();
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Проверка разрешения на редактирование/удаление
     *
     * @param int $groupId ID группы возврата
     * @return array Результат проверки
     */
    private function checkEditPermission($groupId)
    {
        // Находим группу возврата
        $materialStatusGroup = MaterialStatusGroup::findOne([
            'id' => $groupId,
            'status' => MaterialStatusGroup::STATUS_RETURNED_TO_SUPPLIER,
            'deleted_at' => null
        ]);

        if (!$materialStatusGroup) {
            return [
                'success' => false,
                'message' => 'Группа возврата материалов не найдена'
            ];
        }

        // Проверяем, что группа еще не подтверждена
        if ($materialStatusGroup->accepted_user_id !== null || $materialStatusGroup->accepted_at !== null) {
            return [
                'success' => false,
                'message' => 'Нельзя изменять подтвержденный возврат'
            ];
        }

        // Проверка авторства - только создатель может редактировать
        if ($materialStatusGroup->add_user_id != Yii::$app->user->id) {
            return [
                'success' => false,
                'message' => 'Вы можете изменять только свои записи'
            ];
        }

        return [
            'success' => true,
            'group' => $materialStatusGroup
        ];
    }
}
