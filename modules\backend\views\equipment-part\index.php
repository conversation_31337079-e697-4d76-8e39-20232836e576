<?php

use app\common\models\EquipmentPart;
use yii\helpers\Html;
use yii\widgets\Pjax;
use yii\web\View;
use app\assets\DataTablesAsset;

DataTablesAsset::register($this);

$this->title = Yii::t("app", "equipment_parts");
$this->params['breadcrumbs'][] = $this->title;

$searchLabel = Yii::t("app", "search:");
$lengthMenuLabel = Yii::t("app", "Show _MENU_ entries");
$zeroRecordsLabel = Yii::t("app", "Nothing found");
$infoLabel = Yii::t("app", "Showing _PAGE_ to _PAGES_ of _MAX_ items");
$infoEmptyLabel = Yii::t("app", "Nothing found");
$infoFilteredLabel = Yii::t("app", "(filtered from _MAX_ records)");
$all = Yii::t("app", "all");
$active = Yii::t("app", "active");
$inactive = Yii::t("app", "inactive");
$reserve = Yii::t("app", "reserve");
$purchased = Yii::t("app", "purchased");
$fromReserve = Yii::t("app", "reserve");

$statuses = EquipmentPart::getStatusLabels();
$sourceTypes = EquipmentPart::getSourceTypeLabels();
?>

<style>
    #part_filter.select2, #source_type_filter.select2, #equipment_filter.select2 {
        min-width: 145px !important;
        width: 100% !important;
    }

    /* Стили для контейнера Select2 (если используется библиотека Select2) */
    #part_filter + .select2-container, #source_type_filter + .select2-container, #equipment_filter + .select2-container {
        width: 145px !important;
    }

    /* Стили для полей ввода даты */
    input[type="date"].form-control {
        width: 150px;
    }

    /* Отступы между элементами */
    .d-flex.gap-2 {
        gap: 0.5rem !important;
    }
</style>

<div class="card-body">
    <div class="row align-items-center mb-3">
        <div class="col-md-6">
            <h4 class="my-0"><?= Html::encode($this->title) ?></h4>
        </div>
        <div class="col-md-6 text-right">
            <div class="d-flex justify-content-end align-items-center gap-2">
                <?php if (Yii::$app->user->can('admin')): ?>
                    <a href="#" class="btn btn-primary part-create" data-toggle="modal" data-target="#ideal-mini-modal">
                        <?= Yii::t("app", "add_part") ?>
                    </a>
                    <a href="<?= \yii\helpers\Url::to(['/backend/equipment/index']) ?>" class="btn btn-success">
                        <?= Yii::t("app", "equipment") ?>
                    </a>
                <?php endif ?>
            </div>
        </div>
    </div>



    <?php Pjax::begin(['id' => 'part-grid-pjax']); ?>
    <?php if($result): ?>
        <div>
            <table id="part-grid-view" class="table table-bordered table-striped compact">
                <thead>
                    <th><?= Yii::t("app", "Photo") ?></th>
                    <th><?= Yii::t("app", "Name") ?></th>
                    <th><?= Yii::t("app", "Quantity") ?></th>
                    <th><?= Yii::t("app", "Price") ?></th>
                    <th><?= Yii::t("app", "last_purchase_date") ?></th>
                    <th><?= Yii::t("app", "Status") ?></th>
                    <th><?= Yii::t("app", "comment") ?></th>
                    <th><?= Yii::t("app", "actions") ?></th>
                </thead>
                <tbody>
                <?php foreach ($result as $model): ?>
                    <tr>
                        <td>
                            <?php if ($model['photo']): ?>
                                <img src="/uploads/equipment_parts/<?= Html::encode($model['photo']) ?>"
                                     alt="Part"
                                     style="max-width: 50px; cursor: pointer;"
                                     class="part-photo"
                                     data-id="<?= $model['id'] ?>">
                            <?php endif; ?>
                        </td>
                        <td><?= Html::encode($model['name']) ?></td>
                        <td>
                            <?php
                            $totalQuantity = $model['quantity'] ?? 0;
                            $assignedQuantity = $model['assigned_quantity'] ?? 0;
                            $availableQuantity = $totalQuantity - $assignedQuantity;
                            ?>
                            <span class="badge badge-<?= $totalQuantity > 0 ? 'success' : 'danger' ?>">
                                <?= Yii::t('app', 'Total') ?>: <?= $totalQuantity ?>
                            </span>
                            <?php if (isset($model['assigned_quantity'])): ?>
                                <br><span class="badge badge-warning">
                                    <?= Yii::t('app', 'Assigned') ?>: <?= $assignedQuantity ?>
                                </span>
                                <br><span class="badge badge-<?= $availableQuantity > 0 ? 'info' : 'secondary' ?>">
                                    <?= Yii::t('app', 'Available') ?>: <?= $availableQuantity ?>
                                </span>
                            <?php endif; ?>
                        </td>
                        <td><?= $model['price'] ? : 0  ?></td>
                        <td data-order="<?= $model['last_purchase_date'] ? strtotime($model['last_purchase_date']) : 0 ?>">
                            <?= $model['last_purchase_date'] ? date('d.m.Y', strtotime($model['last_purchase_date'])) : '-' ?>
                        </td>
                        <td>
                            <?php if (isset($model['status'])): ?>
                                <span class="badge badge-<?= $model['status'] == EquipmentPart::STATUS_INACTIVE ? 'danger' : ($model['status'] == EquipmentPart::STATUS_ACTIVE ? 'success' : 'warning') ?>">
                                    <?= $statuses[$model['status']] ?? Yii::t('app', 'unknown') ?>
                                </span>
                            <?php else: ?>
                                <span class="badge badge-info"><?= Yii::t('app', 'mixed') ?></span>
                            <?php endif; ?>
                        </td>
                        <td><?= Html::encode($model['comment'] ?? '-') ?></td>
                        <td>
                            <div class="dropdown d-inline">
                                <a href="#" class="badge badge-info dropdown-toggle" data-toggle="dropdown">
                                    <?php echo Yii::t("app", "detail"); ?>
                                </a>
                                <div class="dropdown-menu">
                                    <?php if (!isset($model['deleted_at']) || $model['deleted_at'] == NULL): ?>
                                        <a href="#" class="dropdown-item part-update" data-toggle="modal" data-target="#ideal-mini-modal" data-id="<?= Html::encode($model['id']) ?>">
                                            <?= Yii::t("app", "edit") ?>
                                        </a>
                                        <a href="#" class="dropdown-item part-income" data-toggle="modal" data-target="#ideal-mini-modal" data-id="<?= Html::encode($model['id']) ?>">
                                            <?= Yii::t("app", "income") ?>
                                        </a>
                                        <a href="<?= \yii\helpers\Url::to(['/backend/equipment-part/history', 'id' => $model['id']]) ?>" class="dropdown-item">
                                            <?= Yii::t("app", "history") ?>
                                        </a>
                                        <a href="#" class="dropdown-item part-defect" data-toggle="modal" data-target="#ideal-mini-modal" data-id="<?= Html::encode($model['id']) ?>">
                                            <?= Yii::t("app", "defect") ?>
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </td>
                    </tr>
                <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <p><?= Yii::t('app', 'no_data_available') ?></p>
    <?php endif; ?>
    <?php Pjax::end(); ?>
</div>

<div id="one" data-text="<?= Yii::t("app", "part_information") ?>"></div>
<div id="two" data-text="<?= Yii::t("app", "change_status") ?>"></div>
<div id="three" data-text="<?= Yii::t("app", "history") ?>"></div>
<div id="four" data-text="<?= Yii::t("app", "attach_to_equipment") ?>"></div>
<div id="five" data-text="<?= Yii::t("app", "detach_from_equipment") ?>"></div>
<div id="six" data-text="<?= Yii::t("app", "income") ?>"></div>
<div id="seven" data-text="<?= Yii::t("app", "defect") ?>"></div>
<?php
$js = <<<JS
(function($) {
    var one = $('#one').attr('data-text');
    var two = $('#two').attr('data-text');
    var three = $('#three').attr('data-text');
    var four = $('#four').attr('data-text');
    var five = $('#five').attr('data-text');
    var six = $('#six').attr('data-text');
    var seven = $('#seven').attr('data-text');

    var searchLabel = "{$searchLabel}";
    var lengthMenuLabel = "{$lengthMenuLabel}";
    var zeroRecordsLabel = "{$zeroRecordsLabel}";
    var infoLabel = "{$infoLabel}";
    var infoEmptyLabel = "{$infoEmptyLabel}";
    var infoFilteredLabel = "{$infoFilteredLabel}";

    function initializeDataTable() {
        if ($.fn.DataTable.isDataTable('#part-grid-view')) {
            $('#part-grid-view').DataTable().destroy();
        }

        $('#part-grid-view').DataTable({
            "language": {
                "search": searchLabel,
                "lengthMenu": lengthMenuLabel,
                "zeroRecords": zeroRecordsLabel,
                "info": infoLabel,
                "infoEmpty": infoEmptyLabel,
                "infoFiltered": infoFilteredLabel
            },
            "pageLength": 50,
            "columnDefs": [
                {
                    "targets": [0, 6],
                    "orderable": false
                },
                {
                    "targets": 3,
                    "render": function(data, type, row) {
                        if (type === 'display' && data !== '-') {
                            return parseFloat(data).toLocaleString('en-US', { maximumFractionDigits: 2 });
                        }
                        return data;
                    }
                },
                {
                    "targets": 4,
                    "render": function(data, type, row) {
                        if (type === 'display' && data !== '-' && data) {
                            return moment(data).format('DD.MM.YYYY');
                        }
                        return data;
                    }
                }
            ]
        });
    }

    function initializeSearch() {
        $('#search-button').on('click', function() {
            var status = $('#part_filter').val();
            var sourceType = $('#source_type_filter').val();
            var equipmentId = $('#equipment_filter').val();

            $.ajax({
                url: '/backend/equipment-part/search',
                type: 'POST',
                data: {
                    status: status,
                    source_type: sourceType,
                    equipment_id: equipmentId
                },
                success: function(response) {
                    if (response.status === 'success') {
                        $('#part-grid-pjax').html(response.content);
                        initializeDataTable();
                    } else {
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Search error:', error);
                    alert("<?= Yii::t('app', 'Error occurred while searching') ?>");
                }
            });
        });
    }

    function initializeSelect2() {
        $('.select2').select2({
            width: '100%',
            language: {
                noResults: function() {
                    return "<?= Yii::t('app', 'No results found') ?>";
                },
                searching: function() {
                    return "<?= Yii::t('app', 'Searching...') ?>";
                }
            },
            allowClear: true,
            placeholder: function() {
                return $(this).data('placeholder');
            }
        });
    }

    function initializeActionButtons() {
        // Кнопка "Архивировать"
        $('#btn-archive').on('click', function(e) {
            e.preventDefault();
            alert('<?= Yii::t("app", "Archive functionality will be implemented in the future") ?>');
        });

        // Кнопка "Поделиться"
        $('#btn-share').on('click', function(e) {
            e.preventDefault();
            alert('<?= Yii::t("app", "Share functionality will be implemented in the future") ?>');
        });
    }

    function initializeAll() {
        initializeDataTable();
        initializeSelect2();
        initializePartCreate();
        initializePartUpdate();
        initializePartPhoto();
        initializePartStatus();
        initializePartAttach();
        initializePartDetach();
        initializePartIncome();
        initializePartDefect();
        initializeActionButtons();
        initializeSearch();
        initializeDropdown();
    }

    // Функция для получения параметров из URL
    function getUrlParameter(name) {
        name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
        var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
        var results = regex.exec(location.search);
        return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
    }

    // Функция для очистки ошибок валидации
    function clearValidationErrors() {
        $('.form-control').removeClass('is-invalid');
        $('.invalid-feedback').hide().html('');
    }

    // Функция для отображения ошибок валидации
    function displayValidationErrors(errors) {
        clearValidationErrors();
        if (errors) {
            $.each(errors, function(field, messages) {
                var input = $('[name="' + field + '"]');
                var errorContainer = $('#' + field + '-error');

                if (input.length && errorContainer.length) {
                    input.addClass('is-invalid');
                    errorContainer.html(Array.isArray(messages) ? messages.join('<br>') : messages).show();
                }
            });
        }
    }

    // Initialize everything on first load
    initializeAll();

    // Если в URL есть параметр equipment_id, устанавливаем соответствующее значение в фильтре
    var equipmentIdParam = getUrlParameter('equipment_id');

    if (equipmentIdParam) {
        setTimeout(function() {
            $('#equipment_filter').val(equipmentIdParam).trigger('change');
            // Запускаем поиск с выбранным фильтром
            $('#search-button').trigger('click');
        }, 500); // Небольшая задержка для уверенности, что select2 инициализирован
    }

    // Re-initialize after PJAX reloads
    $(document).on('pjax:success', function() {
        initializeAll();

        // После перезагрузки через PJAX снова проверяем параметр в URL
        var equipmentIdParam = getUrlParameter('equipment_id');

        if (equipmentIdParam) {
            setTimeout(function() {
                $('#equipment_filter').val(equipmentIdParam).trigger('change');
                // Запускаем поиск с выбранным фильтром
                $('#search-button').trigger('click');
            }, 500); // Небольшая задержка для уверенности, что select2 инициализирован
        }
    });

    function initializeDropdown() {
        $(document).off('click.dropdown').on('click.dropdown', '.dropdown-toggle', function(e) {
            e.preventDefault();
            e.stopPropagation();
            var dropdownMenu = $(this).siblings('.dropdown-menu');
            $('.dropdown-menu').not(dropdownMenu).removeClass('show');
            dropdownMenu.toggleClass('show');
        });

        $(document).off('click.dropdown-item').on('click.dropdown-item', '.dropdown-item:not([href^="/"])', function(e) {
            e.preventDefault();
            e.stopPropagation();
        });

        $(document).off('click.dropdown-close').on('click.dropdown-close', function(e) {
            if (!$(e.target).closest('.dropdown').length) {
                $('.dropdown-menu').removeClass('show');
            }
        });
    }

    function initializePartCreate() {
        $(document).off('click.part-create').on('click.part-create', '.part-create', function() {
            $.ajax({
                url: '/backend/equipment-part/create',
                dataType: 'json',
                type: 'GET',
                success: function(response) {
                    $('#ideal-mini-modal .modal-title').html(one);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass("part-create-button");
                    initializeSelect2();
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.part-create-button').on('click.part-create-button', '.part-create-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                clearValidationErrors();
                var formData = new FormData($('#part-create-form')[0]);
                $.ajax({
                    url: '/backend/equipment-part/create',
                    dataType: 'json',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response && response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({
                                container: '#part-grid-pjax',
                                complete: function() {
                                    initializeDataTable();
                                }
                            });
                        } else {
                            button.prop('disabled', false);
                            if (response.errors) {
                                displayValidationErrors(response.errors);
                            }
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        button.prop('disabled', false);
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                    }
                });
            }
        });
    }

    function initializePartUpdate() {
        $(document).off('click.part-update').on('click.part-update', '.part-update', function() {
            var id = $(this).data('id');
            $.ajax({
                url: '/backend/equipment-part/update',
                dataType: 'json',
                type: 'GET',
                data: {id: id},
                success: function(response) {
                    $('#ideal-mini-modal .modal-title').html(one);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass("part-update-button");
                    initializeSelect2();
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.part-update-button').on('click.part-update-button', '.part-update-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                clearValidationErrors();
                var formData = new FormData($('#part-update-form')[0]);
                $.ajax({
                    url: '/backend/equipment-part/update',
                    dataType: 'json',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response && response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({
                                container: '#part-grid-pjax',
                                complete: function() {
                                    initializeDataTable();
                                }
                            });
                        } else {
                            button.prop('disabled', false);
                            if (response.errors) {
                                displayValidationErrors(response.errors);
                            }
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        button.prop('disabled', false);
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                    }
                });
            }
        });
    }

    function initializePartPhoto() {
        $(document).off('click.part-photo');

        $(document).on('click.part-photo', '.part-photo', function() {
            var id = $(this).data('id');
            $.ajax({
                url: '/backend/equipment-part/get-photo',
                type: 'GET',
                data: {id: id},
                success: function(response) {
                    if (response.status === 'success') {
                        $('#ideal-large-modal-without-save .modal-title').text(response.name);
                        var content = '<div class="text-center"><img src="' + response.photo + '" alt="" style="max-width: 100%;"></div>';
                        $('#ideal-large-modal-without-save .modal-body').html(content);
                        $('#ideal-large-modal-without-save').modal('show');
                    }
                },
                error: function() {
                    console.error('Error loading photo');
                }
            });
        });

        // Очистка содержимого при закрытии
        $('#ideal-large-modal-without-save').on('hidden.bs.modal', function () {
            $('#ideal-large-modal-without-save .modal-body').html('');
            $('#ideal-large-modal-without-save .modal-title').text('');
        });
    }

    function initializePartStatus() {
        $(document).off('click.part-change-status').on('click.part-change-status', '.part-change-status', function() {
            var id = $(this).data('id');
            $.ajax({
                url: '/backend/equipment-part/change-status',
                type: 'GET',
                data: {id: id},
                success: function(response) {
                    $('#ideal-mini-modal .modal-title').html(two);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass('part-status-save');
                    initializeSelect2();
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.part-status-save').on('click.part-status-save', '.part-status-save', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var id = $('.part-change-status').data('id');
                var formData = new FormData($('#part-status-form')[0]);

                $.ajax({
                    url: '/backend/equipment-part/change-status?id=' + id,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({
                                container: '#part-grid-pjax',
                                complete: function() {
                                    initializeDataTable();
                                }
                            });
                        } else {
                            button.prop('disabled', false);
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        button.prop('disabled', false);
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                    }
                });
            }
        });
    }

    function initializePartAttach() {
        $(document).off('click.part-attach').on('click.part-attach', '.part-attach', function() {
            var id = $(this).data('id');
            $.ajax({
                url: '/backend/equipment-part/attach',
                type: 'GET',
                data: {id: id},
                success: function(response) {
                    $('#ideal-mini-modal .modal-title').html(four);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass('part-attach-save');
                    initializeSelect2();
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.part-attach-save').on('click.part-attach-save', '.part-attach-save', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var id = $('.part-attach').data('id');
                var formData = new FormData($('#part-attach-form')[0]);

                $.ajax({
                    url: '/backend/equipment-part/attach?id=' + id,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({
                                container: '#part-grid-pjax',
                                complete: function() {
                                    initializeDataTable();
                                }
                            });
                        } else {
                            button.prop('disabled', false);
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        button.prop('disabled', false);
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                    }
                });
            }
        });
    }

    function initializePartDetach() {
        $(document).off('click.part-detach').on('click.part-detach', '.part-detach', function() {
            var id = $(this).data('id');
            $.ajax({
                url: '/backend/equipment-part/detach',
                type: 'GET',
                data: {id: id},
                success: function(response) {
                    $('#ideal-mini-modal .modal-title').html(five);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass('part-detach-save');
                    initializeSelect2();
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.part-detach-save').on('click.part-detach-save', '.part-detach-save', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var id = $('.part-detach').data('id');
                var formData = new FormData($('#part-detach-form')[0]);

                $.ajax({
                    url: '/backend/equipment-part/detach?id=' + id,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({
                                container: '#part-grid-pjax',
                                complete: function() {
                                    initializeDataTable();
                                }
                            });
                        } else {
                            button.prop('disabled', false);
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        button.prop('disabled', false);
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                    }
                });
            }
        });
    }

    function initializePartIncome() {
        $(document).off('click.part-income').on('click.part-income', '.part-income', function() {
            var id = $(this).data('id');
            $.ajax({
                url: '/backend/equipment-part/income',
                type: 'GET',
                data: {id: id},
                success: function(response) {
                    $('#ideal-mini-modal .modal-title').html(six);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass('part-income-save');
                    initializeSelect2();
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.part-income-save').on('click.part-income-save', '.part-income-save', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                clearValidationErrors();
                var formData = new FormData($('#part-income-form')[0]);

                $.ajax({
                    url: '/backend/equipment-part/income',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({
                                container: '#part-grid-pjax',
                                complete: function() {
                                    initializeDataTable();
                                }
                            });
                        } else {
                            button.prop('disabled', false);
                            if (response.errors) {
                                displayValidationErrors(response.errors);
                            }
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        button.prop('disabled', false);
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                    }
                });
            }
        });
    }

    function initializePartDefect() {
        $(document).off('click.part-defect').on('click.part-defect', '.part-defect', function() {
            var id = $(this).data('id');
            $.ajax({
                url: '/backend/equipment-part/defect',
                type: 'GET',
                data: {id: id},
                success: function(response) {
                    $('#ideal-mini-modal .modal-title').html(seven);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass('part-defect-save');
                    initializeSelect2();
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.part-defect-save').on('click.part-defect-save', '.part-defect-save', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                clearValidationErrors();
                var formData = new FormData($('#part-defect-form')[0]);

                $.ajax({
                    url: '/backend/equipment-part/defect',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({
                                container: '#part-grid-pjax',
                                complete: function() {
                                    initializeDataTable();
                                }
                            });
                        } else {
                            button.prop('disabled', false);
                            if (response.errors) {
                                displayValidationErrors(response.errors);
                            }
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        button.prop('disabled', false);
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                    }
                });
            }
        });
    }
})(jQuery);
JS;
$this->registerJs($js, View::POS_END);
?>
