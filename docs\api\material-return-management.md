# API для управления возвратами материалов

## Обзор

Новая функциональность позволяет обновлять и удалять неподтвержденные возвраты материалов через API.

## Ограничения

- Можно изменять только **неподтвержденные** возвраты (accepted_at = null, accepted_user_id = null)
- Только **создатель** возврата может его редактировать или удалять
- Все операции логируются через ActionLogger

## Endpoints

### 1. Получение данных возврата для редактирования

**GET** `/api/raw-material/return-material/{id}`

**Параметры:**
- `id` (integer) - ID группы возврата материалов

**Ответ при успехе (200):**
```json
{
  "message": "Данные возврата материалов",
  "data": {
    "group_id": 123,
    "supplier_id": 5,
    "created_at": "2024-01-15 10:30:00",
    "materials": [
      {
        "id": 456,
        "material_id": 10,
        "quantity": 100,
        "material_name": "Сталь листовая",
        "unit_type": 1,
        "unit_type_name": "шт"
      }
    ]
  },
  "code": 200
}
```

**Ошибки:**
- `404` - Группа возврата не найдена
- `403` - Нет прав на редактирование (не создатель или уже подтверждено)

### 2. Обновление возврата материалов

**PUT** `/api/raw-material/return-material/{id}`

**Параметры URL:**
- `id` (integer) - ID группы возврата материалов

**Тело запроса:**
```json
{
  "supplier_id": 5,
  "materials": [
    {
      "material_id": 10,
      "quantity": 150
    },
    {
      "material_id": 15,
      "quantity": 50
    }
  ],
  "description": "Обновленное описание возврата"
}
```

**Валидация:**
- `supplier_id` - обязательное поле, должен существовать в БД
- `materials` - обязательный массив, минимум 1 элемент
- Для каждого материала:
  - `material_id` - обязательное поле, должен существовать в БД
  - `quantity` - обязательное поле, больше 0
  - Для штучных материалов количество должно быть целым числом
- Нельзя дублировать материалы в одном возврате

**Ответ при успехе (200):**
```json
{
  "message": "Возврат материалов успешно обновлен",
  "data": {},
  "code": 200
}
```

**Ошибки:**
- `404` - Группа возврата не найдена
- `403` - Нет прав на редактирование
- `422` - Ошибка валидации
- `500` - Внутренняя ошибка сервера

### 3. Удаление возврата материалов

**DELETE** `/api/raw-material/return-material/{id}`

**Параметры:**
- `id` (integer) - ID группы возврата материалов

**Ответ при успехе (200):**
```json
{
  "message": "Возврат материалов успешно удален",
  "data": {},
  "code": 200
}
```

**Ошибки:**
- `404` - Группа возврата не найдена
- `403` - Нет прав на удаление
- `500` - Внутренняя ошибка сервера

## Логика работы

### Обновление возврата
1. Проверка прав доступа (создатель + неподтвержденный статус)
2. Обновление данных группы возврата (supplier_id)
3. Soft delete старых записей материалов
4. Создание новых записей материалов
5. Логирование действия

### Удаление возврата
1. Проверка прав доступа
2. Soft delete всех записей материалов в группе
3. Soft delete группы возврата
4. Логирование действия

## Примеры использования

### JavaScript/Fetch API

```javascript
// Получение данных возврата
const getReturnData = async (returnId) => {
  const response = await fetch(`/api/raw-material/return-material/${returnId}`, {
    method: 'GET',
    headers: {
      'Authorization': 'Bearer YOUR_TOKEN',
      'Content-Type': 'application/json'
    }
  });
  return await response.json();
};

// Обновление возврата
const updateReturn = async (returnId, data) => {
  const response = await fetch(`/api/raw-material/return-material/${returnId}`, {
    method: 'PUT',
    headers: {
      'Authorization': 'Bearer YOUR_TOKEN',
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(data)
  });
  return await response.json();
};

// Удаление возврата
const deleteReturn = async (returnId) => {
  const response = await fetch(`/api/raw-material/return-material/${returnId}`, {
    method: 'DELETE',
    headers: {
      'Authorization': 'Bearer YOUR_TOKEN',
      'Content-Type': 'application/json'
    }
  });
  return await response.json();
};
```

### cURL примеры

```bash
# Получение данных возврата
curl -X GET "http://your-domain/api/raw-material/return-material/123" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"

# Обновление возврата
curl -X PUT "http://your-domain/api/raw-material/return-material/123" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "supplier_id": 5,
    "materials": [
      {"material_id": 10, "quantity": 150}
    ],
    "description": "Обновленное описание"
  }'

# Удаление возврата
curl -X DELETE "http://your-domain/api/raw-material/return-material/123" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"
```

## Безопасность

- Все endpoints требуют аутентификации через Bearer token
- Проверка роли `raw_keeper` для доступа к контроллеру
- Проверка авторства записи (только создатель может изменять)
- Проверка статуса подтверждения (только неподтвержденные можно изменять)

## Логирование

Все операции логируются в таблицу `action_logs` с типами:
- `update_return_material` - обновление возврата
- `delete_return_material` - удаление возврата
