<?php

use yii\helpers\Html;
use yii\helpers\Url;
use app\common\models\EquipmentPart;
use app\common\models\EquipmentPartHistory;
use app\common\models\EquipmentPartMovement;
use app\common\models\EquipmentPartAssignment;

$this->title = Yii::t('app', 'part_history_on_equipment', [
    'part' => $part->name,
    'equipment' => $equipment->name
]);

$this->params['breadcrumbs'][] = ['label' => Yii::t('app', 'equipment'), 'url' => ['index']];
$this->params['breadcrumbs'][] = ['label' => Html::encode($equipment->name), 'url' => ['view', 'id' => $equipment->id]];
$this->params['breadcrumbs'][] = ['label' => Yii::t('app', 'parts'), 'url' => ['parts', 'id' => $equipment->id]];
$this->params['breadcrumbs'][] = Yii::t('app', 'part_history');

$referer = Yii::$app->request->referrer ?: Url::to(['parts', 'id' => $equipment->id]);
?>

<style>
    .part-info-card {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-left: 4px solid #007bff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .part-photo {
        max-width: 80px;
        max-height: 80px;
        border-radius: 6px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    }
    
    .status-badge {
        font-size: 0.875rem;
        padding: 0.375rem 0.75rem;
        border-radius: 20px;
    }
    
    .history-table th {
        background-color: #f8f9fa;
        border-top: none;
        font-weight: 600;
        color: #495057;
    }
    
    .table-action {
        background-color: rgba(23, 162, 184, 0.1);
    }
    
    .table-defect {
        background-color: rgba(220, 53, 69, 0.1);
    }
    
    .badge-action {
        background-color: #17a2b8;
        color: white;
    }
    
    .badge-defect {
        background-color: #dc3545;
        color: white;
    }
</style>

<div class="equipment-part-history">
    <div class="card-body">
        <!-- Заголовок и кнопка возврата -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h4 class="my-0"><?= Html::encode($this->title) ?></h4>
            <a href="<?= $referer ?>" class="btn btn-primary">
                <i class="fas fa-arrow-left mr-1"></i>
                <?= Yii::t('app', 'back_to_parts') ?>
            </a>
        </div>

        <!-- Информация о запчасти -->
        <div class="part-info-card p-3 mb-4">
            <div class="row align-items-center">
                <div class="col-auto">
                    <?php if ($part->photo): ?>
                        <img src="/uploads/equipment_parts/<?= Html::encode($part->photo) ?>" 
                             alt="<?= Html::encode($part->name) ?>" 
                             class="part-photo">
                    <?php else: ?>
                        <div class="part-photo bg-light d-flex align-items-center justify-content-center">
                            <i class="fas fa-image text-muted"></i>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="col">
                    <h5 class="mb-1"><?= Html::encode($part->name) ?></h5>
                    <p class="text-muted mb-2"><?= Yii::t('app', 'equipment') ?>: <strong><?= Html::encode($equipment->name) ?></strong></p>
                    
                    <!-- Текущий статус и количество -->
                    <div class="d-flex flex-wrap gap-2">
                        <?php foreach ($assignments as $assignment): ?>
                            <?php if ($assignment->active_quantity > 0): ?>
                                <span class="badge status-badge badge-success">
                                    <?= Yii::t('app', 'active') ?>: <?= $assignment->active_quantity ?>
                                </span>
                            <?php endif; ?>
                            <?php if ($assignment->repair_quantity > 0): ?>
                                <span class="badge status-badge badge-warning">
                                    <?= Yii::t('app', 'repair') ?>: <?= $assignment->repair_quantity ?>
                                </span>
                            <?php endif; ?>
                        <?php endforeach; ?>
                        
                        <?php if (empty($assignments) || (array_sum(array_column($assignments, 'active_quantity')) == 0 && array_sum(array_column($assignments, 'repair_quantity')) == 0)): ?>
                            <span class="badge status-badge badge-secondary">
                                <?= Yii::t('app', 'not_assigned') ?>
                            </span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- История операций -->
        <?php if (empty($historyData) && empty($movementsData)): ?>
            <div class="alert alert-info text-center">
                <i class="fas fa-info-circle mr-2"></i>
                <?= Yii::t('app', 'no_history_records_found') ?>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-bordered table-striped history-table">
                    <thead>
                        <tr>
                            <th style="width: 140px;"><?= Yii::t('app', 'date') ?></th>
                            <th style="width: 120px;"><?= Yii::t('app', 'type') ?></th>
                            <th style="width: 100px;"><?= Yii::t('app', 'quantity') ?></th>
                            <th><?= Yii::t('app', 'comment') ?></th>
                            <th style="width: 120px;"><?= Yii::t('app', 'user') ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        // Объединяем историю действий и движения в один массив для сортировки
                        $allRecords = [];

                        // Добавляем историю действий
                        foreach ($historyData as $record) {
                            $allRecords[] = [
                                'type' => 'history',
                                'data' => $record,
                                'created_at' => $record['created_at']
                            ];
                        }

                        // Добавляем движения (брак)
                        foreach ($movementsData as $movement) {
                            $allRecords[] = [
                                'type' => 'movement',
                                'data' => $movement,
                                'created_at' => $movement['created_at']
                            ];
                        }

                        // Сортируем по дате (новые сверху)
                        usort($allRecords, function($a, $b) {
                            return strtotime($b['created_at']) - strtotime($a['created_at']);
                        });
                        ?>

                        <?php foreach ($allRecords as $record): ?>
                            <?php if ($record['type'] == 'history'): ?>
                                <?php $historyRecord = $record['data']; ?>
                                <tr class="table-action">
                                    <td><?= date('d.m.Y H:i', strtotime($historyRecord['created_at'])) ?></td>
                                    <td>
                                        <span class="badge badge-action">
                                            <?php
                                            switch ($historyRecord['action_type']) {
                                                case EquipmentPart::ACTION_INSTALL:
                                                    echo Yii::t('app', 'installation');
                                                    break;
                                                case EquipmentPart::ACTION_REMOVE:
                                                    echo Yii::t('app', 'removal');
                                                    break;
                                                case EquipmentPart::ACTION_RESERVE:
                                                    echo Yii::t('app', 'moved_to_reserve');
                                                    break;
                                                case EquipmentPart::ACTION_REPAIR:
                                                    echo Yii::t('app', 'sent_to_repair');
                                                    break;
                                                case EquipmentPart::ACTION_ACTIVATE:
                                                    echo Yii::t('app', 'returned_from_repair');
                                                    break;
                                                default:
                                                    echo Yii::t('app', 'action');
                                            }
                                            ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if (!empty($historyRecord['quantity'])): ?>
                                            <span class="text-primary font-weight-bold">
                                                <?= number_format($historyRecord['quantity'], 0, ',', ' ') ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?= Html::encode($historyRecord['comment']) ?></td>
                                    <td>
                                        <small class="text-muted">
                                            <?= Html::encode($historyRecord['user_name'] ?? '-') ?>
                                        </small>
                                    </td>
                                </tr>
                            <?php else: ?>
                                <?php $movement = $record['data']; ?>
                                <tr class="table-defect">
                                    <td><?= date('d.m.Y H:i', strtotime($movement['created_at'])) ?></td>
                                    <td>
                                        <span class="badge badge-defect">
                                            <?= Yii::t('app', 'defect') ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="text-danger font-weight-bold">
                                            -<?= number_format($movement['quantity'], 0, ',', ' ') ?>
                                        </span>
                                    </td>
                                    <td><?= Html::encode($movement['comment']) ?></td>
                                    <td>
                                        <small class="text-muted">
                                            <?= Html::encode($movement['user_name'] ?? '-') ?>
                                        </small>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>
